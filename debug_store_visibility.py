#!/usr/bin/env python3
"""
Store Visibility Debug <PERSON>ript

This script helps diagnose why a newly connected store is not appearing in the frontend.
It checks database records, user associations, and filtering logic.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_database_records():
    """Check database for store records"""
    try:
        from database.session import SessionLocal
        from database.crud import StoreCRUD, UserCRUD
        from database.models import Store, User
        from sqlalchemy import text
        
        print("🔍 Checking Database Records")
        print("=" * 50)
        
        db = SessionLocal()
        
        try:
            # Get all users
            print("\n📋 Users in database:")
            users = db.query(User).all()
            for user in users:
                print(f"  - User ID: {user.id}, Username: {user.username}, Email: {user.email}, Active: {user.is_active}")
            
            # Get all stores
            print("\n📋 All stores in database:")
            stores = db.query(Store).all()
            for store in stores:
                print(f"  - Store ID: {store.id}")
                print(f"    User ID: {store.user_id}")
                print(f"    Shopify Store ID: {store.shopify_store_id}")
                print(f"    Store Name: {store.store_name}")
                print(f"    Shop URL: {store.shop_url}")
                print(f"    MyShopify Domain: {store.myshopify_domain}")
                print(f"    Status: {store.status}")
                print(f"    Is Deleted: {store.is_deleted}")
                print(f"    Created: {store.created_at}")
                print("    ---")
            
            # Check filtering logic
            print("\n📋 Testing filtering logic:")
            for user in users:
                print(f"\n  User {user.username} (ID: {user.id}):")
                
                # Test active_only=True (default)
                active_stores = StoreCRUD.get_stores_by_user(db, user.id, active_only=True)
                print(f"    Active stores: {len(active_stores)}")
                for store in active_stores:
                    print(f"      - {store.store_name} ({store.status})")
                
                # Test active_only=False
                all_stores = StoreCRUD.get_stores_by_user(db, user.id, active_only=False)
                print(f"    All stores: {len(all_stores)}")
                for store in all_stores:
                    print(f"      - {store.store_name} ({store.status}, deleted: {store.is_deleted})")
            
            # Check recent stores
            print("\n📋 Recent stores (last 24 hours):")
            recent_stores = db.execute(text("""
                SELECT id, user_id, store_name, shop_url, status, is_deleted, created_at 
                FROM stores 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ORDER BY created_at DESC
            """)).fetchall()
            
            for store in recent_stores:
                print(f"  - Store ID: {store[0]}, User: {store[1]}, Name: {store[2]}")
                print(f"    URL: {store[3]}, Status: {store[4]}, Deleted: {store[5]}")
                print(f"    Created: {store[6]}")
            
            return True
            
        except Exception as e:
            print(f"❌ Database query failed: {str(e)}")
            return False
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def test_api_endpoint():
    """Test the stores API endpoint"""
    try:
        import requests
        import json
        
        print("\n🌐 Testing API Endpoint")
        print("=" * 50)
        
        # You'll need to replace this with a valid JWT token
        print("⚠️  To test the API endpoint, you need to:")
        print("1. Login to your application")
        print("2. Get your JWT token from browser dev tools")
        print("3. Run this test manually with the token")
        
        print("\nExample curl command:")
        print("curl -H 'Authorization: Bearer YOUR_JWT_TOKEN' http://localhost:8000/stores/?include_stats=true")
        
        return True
        
    except Exception as e:
        print(f"❌ API test setup failed: {str(e)}")
        return False

def check_store_status_issues():
    """Check for common store status issues"""
    try:
        from database.session import SessionLocal
        from database.models import Store
        from sqlalchemy import text
        
        print("\n🔧 Checking Store Status Issues")
        print("=" * 50)
        
        db = SessionLocal()
        
        try:
            # Check for stores with problematic statuses
            print("\n📋 Stores with non-active status:")
            non_active = db.execute(text("""
                SELECT id, user_id, store_name, status, is_deleted, created_at 
                FROM stores 
                WHERE status != 'active' OR is_deleted = 1
                ORDER BY created_at DESC
            """)).fetchall()
            
            for store in non_active:
                print(f"  - Store ID: {store[0]}, User: {store[1]}, Name: {store[2]}")
                print(f"    Status: {store[3]}, Deleted: {store[4]}, Created: {store[5]}")
            
            # Check for stores without proper user association
            print("\n📋 Stores with invalid user associations:")
            invalid_users = db.execute(text("""
                SELECT s.id, s.user_id, s.store_name, s.status 
                FROM stores s 
                LEFT JOIN users u ON s.user_id = u.id 
                WHERE u.id IS NULL OR u.is_active = 0
            """)).fetchall()
            
            for store in invalid_users:
                print(f"  - Store ID: {store[0]}, User ID: {store[1]}, Name: {store[2]}")
                print(f"    Status: {store[3]} (User not found or inactive)")
            
            return True
            
        except Exception as e:
            print(f"❌ Status check failed: {str(e)}")
            return False
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Status check setup failed: {str(e)}")
        return False

def main():
    """Main diagnostic function"""
    print("🔍 Store Visibility Diagnostic Tool")
    print("This tool helps diagnose why stores aren't appearing in the frontend")
    print()
    
    success = True
    
    # Check database records
    if not check_database_records():
        success = False
    
    # Check store status issues
    if not check_store_status_issues():
        success = False
    
    # Test API endpoint
    if not test_api_endpoint():
        success = False
    
    if success:
        print("\n✅ Diagnostic completed successfully!")
        print("\nCommon solutions:")
        print("1. Check if store status is 'active' and is_deleted is False")
        print("2. Verify user_id association is correct")
        print("3. Check if frontend is calling the correct API endpoint")
        print("4. Verify JWT token is valid and not expired")
        print("5. Check browser console for JavaScript errors")
        print("6. Try refreshing the page or clearing browser cache")
    else:
        print("\n❌ Diagnostic found issues - check the output above")

if __name__ == "__main__":
    main()
