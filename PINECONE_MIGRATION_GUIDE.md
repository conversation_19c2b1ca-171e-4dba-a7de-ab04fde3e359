# Pinecone Account Migration Guide

This guide explains how to migrate your application to a new Pinecone account after the recent changes have been implemented.

## ✅ Migration Fixes Implemented

The following issues have been resolved to ensure smooth migration:

### 1. **Database Schema Updates**
- ✅ `pinecone_assistant_id` column now allows NULL values during migration
- ✅ Added validation to prevent reusing assistants with invalid Pinecone IDs
- ✅ Enhanced error handling for NULL/invalid file IDs

### 2. **Assistant Creation Logic**
- ✅ Added validation for Pinecone assistant accessibility
- ✅ Improved error handling during assistant initialization
- ✅ Automatic recreation of assistants when Pinecone IDs are invalid

### 3. **Data Synchronization**
- ✅ Enhanced file existence checking before operations
- ✅ Better error handling for invalid file references
- ✅ Automatic cleanup of orphaned file references

### 4. **Health Monitoring**
- ✅ Updated health checks to properly validate file IDs
- ✅ Improved status reporting for migrated assistants

## 🚀 Migration Process

### Step 1: Run the Migration Script

Execute the migration script to update your database:

```bash
python migrate_pinecone_account.py
```

This script will:
- ✅ Update database schema to allow NULL Pinecone assistant IDs
- ✅ Clear all invalid Pinecone resource references
- ✅ Verify connection to your new Pinecone account
- ✅ Prepare the database for assistant recreation

### Step 2: Restart Your Application

```bash
python main.py
```

### Step 3: Verify Migration

1. **Check Application Logs**: Monitor for any errors during startup
2. **Test Store Connections**: Existing stores should automatically recreate their assistants
3. **Verify Data Sync**: All Shopify data will be re-uploaded to the new Pinecone account

## 🔍 What Happens During Migration

### Automatic Assistant Recreation
When users access their stores after migration:

1. **Detection**: System detects missing/invalid Pinecone assistant ID
2. **Recreation**: New Pinecone assistant is created in the new account
3. **Data Sync**: All Shopify data (products, orders, customers) is re-uploaded
4. **Database Update**: New Pinecone resource IDs are stored in the database

### Data Preservation
- ✅ All Shopify store connections remain intact
- ✅ User accounts and authentication are unaffected
- ✅ Store configurations and settings are preserved
- ✅ Only Pinecone-specific resources are recreated

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### Issue: "Assistant creation failed"
**Solution**: Check your new Pinecone API key in `.env` file

#### Issue: "File upload errors"
**Solution**: Verify Pinecone account has sufficient quota and limits

#### Issue: "Database constraint errors"
**Solution**: Ensure migration script completed successfully

#### Issue: "Assistant not accessible"
**Solution**: The system will automatically recreate the assistant

### Monitoring Commands

Check assistant status:
```bash
# View application logs
tail -f logs/app.log

# Check database status
mysql -u root -p pinecone_ecommerce -e "SELECT id, assistant_name, status, pinecone_assistant_id FROM assistants;"
```

## 📊 Expected Behavior After Migration

### For Existing Stores:
1. **First Access**: Assistant will be automatically recreated
2. **Data Sync**: All store data will be re-uploaded (may take a few minutes)
3. **Normal Operation**: Full functionality restored after initial sync

### For New Stores:
1. **OAuth Flow**: Works normally with new Pinecone account
2. **Assistant Creation**: Creates new assistants in new account
3. **Data Upload**: Uploads data to new Pinecone account

## ⚠️ Important Notes

### Data Migration
- **No data loss**: All Shopify data is preserved and will be re-synced
- **Temporary downtime**: Brief period while assistants are recreated
- **Automatic process**: No manual intervention required for individual stores

### Performance Considerations
- **Initial sync**: May take longer as all data is re-uploaded
- **Quota usage**: Monitor Pinecone account limits during bulk recreation
- **Rate limiting**: System respects Pinecone API rate limits

## 🎯 Success Indicators

Migration is successful when:
- ✅ Migration script completes without errors
- ✅ Application starts without database constraint errors
- ✅ Stores can create new assistants
- ✅ Data synchronization works for all store types
- ✅ Chat functionality works with recreated assistants

## 📞 Support

If you encounter issues:
1. Check the application logs for specific error messages
2. Verify your new Pinecone API key is correct
3. Ensure your new Pinecone account has sufficient quota
4. Run the migration script again if needed (it's safe to re-run)

The migration process is designed to be robust and handle edge cases automatically. Most issues resolve themselves during the automatic assistant recreation process.
