"""
OAuth Utilities Module

Provides HMAC verification and OAuth security utilities for Shopify OAuth flow.
"""

import hmac
import hashlib
import logging
from urllib.parse import urlencode
from typing import Dict, Any
from config import settings


logger = logging.getLogger(__name__)


def verify_hmac(params: Dict[str, Any], hmac_header: str) -> bool:
    """
    Verify Shopify HMAC signature to secure OAuth callback
    
    Args:
        params: Query parameters from OAuth callback
        hmac_header: HMAC signature from Shopify
        
    Returns:
        bool: True if HMAC is valid, False otherwise
    """
    try:
        logger.info("Starting HMAC verification for OAuth callback")
        
        # Remove hmac from params for verification
        params_copy = {k: v for k, v in params.items() if k != 'hmac'}
        
        # Create message string from sorted parameters
        message = urlencode(sorted(params_copy.items()))
        logger.debug(f"HMAC message: {message}")
        
        # Create HMAC signature
        secret = settings.shopify_api_secret.encode('utf-8')
        computed_hmac = hmac.new(secret, message.encode('utf-8'), hashlib.sha256).hexdigest()
        
        # Compare signatures using constant-time comparison
        is_valid = hmac.compare_digest(computed_hmac, hmac_header)
        
        if is_valid:
            logger.info("HMAC verification successful")
        else:
            logger.warning("HMAC verification failed")
            logger.debug(f"Expected: {computed_hmac}, Received: {hmac_header}")
        
        return is_valid
        
    except Exception as e:
        logger.error(f"Error during HMAC verification: {e}")
        return False


def validate_oauth_callback_params(params: Dict[str, Any]) -> Dict[str, str]:
    """
    Validate required OAuth callback parameters
    
    Args:
        params: Query parameters from OAuth callback
        
    Returns:
        Dict with validation results
        
    Raises:
        ValueError: If required parameters are missing
    """
    required_params = ["hmac", "shop", "code"]
    missing_params = []
    
    for param in required_params:
        if param not in params or not params[param]:
            missing_params.append(param)
    
    if missing_params:
        raise ValueError(f"Missing required OAuth parameters: {', '.join(missing_params)}")
    
    # Validate shop domain format
    shop = params["shop"]
    if not shop.endswith(".myshopify.com"):
        raise ValueError("Invalid shop domain format")
    
    return {
        "shop": shop,
        "code": params["code"],
        "hmac": params["hmac"],
        "host": params.get("host", ""),
        "timestamp": params.get("timestamp", "")
    }


def generate_oauth_state() -> str:
    """
    Generate a random state parameter for OAuth security
    
    Returns:
        str: Random state string
    """
    import secrets
    return secrets.token_urlsafe(32)


def validate_oauth_state(received_state: str, expected_state: str) -> bool:
    """
    Validate OAuth state parameter to prevent CSRF attacks
    
    Args:
        received_state: State received from OAuth callback
        expected_state: Expected state value
        
    Returns:
        bool: True if states match, False otherwise
    """
    if not received_state or not expected_state:
        return False
    
    return hmac.compare_digest(received_state, expected_state)


def extract_shop_domain(shop_param: str) -> str:
    """
    Extract and validate shop domain from OAuth parameters
    
    Args:
        shop_param: Shop parameter from OAuth flow
        
    Returns:
        str: Validated shop domain
        
    Raises:
        ValueError: If shop domain is invalid
    """
    if not shop_param:
        raise ValueError("Shop parameter is required")
    
    shop = shop_param.strip().lower()
    
    # Remove protocol if present
    if shop.startswith(('http://', 'https://')):
        shop = shop.split('://', 1)[1]
    
    # Ensure it ends with .myshopify.com
    if not shop.endswith('.myshopify.com'):
        raise ValueError("Invalid Shopify domain format")
    
    return shop


def log_oauth_event(event_type: str, shop: str, details: Dict[str, Any] = None):
    """
    Log OAuth events for debugging and audit purposes
    
    Args:
        event_type: Type of OAuth event (install, callback, token_exchange, etc.)
        shop: Shop domain
        details: Additional event details
    """
    log_data = {
        "event": event_type,
        "shop": shop,
        "timestamp": logger.handlers[0].formatter.formatTime(logger.makeRecord(
            logger.name, logging.INFO, __file__, 0, "", (), None
        )) if logger.handlers else None
    }
    
    if details:
        log_data.update(details)
    
    logger.info(f"OAuth Event: {log_data}")
