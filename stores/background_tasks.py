"""
Background tasks for post-OAuth store setup and data synchronization
"""
import logging
import threading
from sqlalchemy.orm import Session

from database.session import get_db
from database.crud import StoreCRUD, AssistantCRUD
from .service import StoreService
from assistants.multi_store_assistant import multi_store_manager

logger = logging.getLogger(__name__)

def trigger_post_oauth_setup(store_id: int, user_id: int):
    """
    Trigger post-OAuth setup in a background thread (DEPRECATED - includes automatic sync)

    This function is deprecated and should not be used for new store connections.
    Use trigger_post_oauth_assistant_setup() instead for assistant-only setup.

    Args:
        db: Database session
        store_id: ID of the newly connected store
        user_id: ID of the user who connected the store
    """
    def run_setup():
        try:
            # Create a new database session for the background thread
            with next(get_db()) as bg_db:
                post_oauth_setup_task(bg_db, store_id, user_id)
        except Exception as e:
            logger.error(f"Background setup failed for store {store_id}: {str(e)}", exc_info=True)

    # Start the setup in a background thread
    setup_thread = threading.Thread(target=run_setup, daemon=True)
    setup_thread.start()
    logger.info(f"Background setup thread started for store {store_id}")

def trigger_post_oauth_assistant_setup(store_id: int, user_id: int):
    """
    Trigger post-OAuth assistant setup in a background thread (WITHOUT automatic data sync)

    Args:
        store_id: ID of the newly connected store
        user_id: ID of the user who connected the store
    """
    def run_assistant_setup():
        try:
            # Create a new database session for the background thread
            with next(get_db()) as bg_db:
                post_oauth_assistant_setup_task(bg_db, store_id, user_id)
        except Exception as e:
            logger.error(f"Background assistant setup failed for store {store_id}: {str(e)}", exc_info=True)

    # Start the setup in a background thread
    setup_thread = threading.Thread(target=run_assistant_setup, daemon=True)
    setup_thread.start()
    logger.info(f"Background assistant setup thread started for store {store_id}")

def post_oauth_setup_task(db: Session, store_id: int, user_id: int):
    """
    Perform post-OAuth setup tasks (DEPRECATED - includes automatic sync):
    1. Sync store data from Shopify
    2. Create/update Pinecone assistant
    3. Update store status

    This function is deprecated for new store connections.
    Use post_oauth_assistant_setup_task() instead.

    Args:
        db: Database session
        store_id: ID of the newly connected store
        user_id: ID of the user who connected the store
    """
    logger.info(f"Starting post-OAuth setup for store {store_id}")

    try:
        # Get the store
        store = StoreCRUD.get_store_by_id(db, store_id)
        if not store or store.user_id != user_id:
            logger.error(f"Store {store_id} not found or access denied for user {user_id}")
            return

        # Update store status to syncing
        StoreCRUD.update_store(db, store_id, status='syncing')
        logger.info(f"Store {store_id} status updated to 'syncing'")

        # Step 1: Sync store data from Shopify
        logger.info(f"Step 1: Syncing data for store {store_id}")
        store_service = StoreService()
        sync_success = store_service.sync_store_data(db, store_id, user_id, force=True)

        if not sync_success:
            logger.error(f"Data sync failed for store {store_id}")
            StoreCRUD.update_store(db, store_id, status='error')
            return

        logger.info(f"Data sync completed for store {store_id}")

        # Step 2: Create or update Pinecone assistant
        logger.info(f"Step 2: Setting up Pinecone assistant for store {store_id}")
        assistant_success = setup_pinecone_assistant(db, store_id, user_id)

        if not assistant_success:
            logger.warning(f"Assistant setup failed for store {store_id}, but store sync was successful")
            StoreCRUD.update_store(db, store_id, status='active')  # Still mark as active since data sync worked
            return

        # Step 3: Update store status to active
        StoreCRUD.update_store(db, store_id, status='active')
        logger.info(f"Post-OAuth setup completed successfully for store {store_id}")

    except Exception as e:
        logger.error(f"Post-OAuth setup failed for store {store_id}: {str(e)}", exc_info=True)
        try:
            StoreCRUD.update_store(db, store_id, status='error')
        except Exception as status_error:
            logger.error(f"Failed to update store status to error: {status_error}")

def post_oauth_assistant_setup_task(db: Session, store_id: int, user_id: int):
    """
    Perform post-OAuth assistant setup tasks WITHOUT automatic data sync:
    1. Create Pinecone assistant (without data sync)
    2. Update store status to active

    Args:
        db: Database session
        store_id: ID of the newly connected store
        user_id: ID of the user who connected the store
    """
    logger.info(f"Starting post-OAuth assistant setup for store {store_id} (no automatic sync)")

    try:
        # Get the store
        store = StoreCRUD.get_store_by_id(db, store_id)
        if not store or store.user_id != user_id:
            logger.error(f"Store {store_id} not found or access denied for user {user_id}")
            return

        # Create Pinecone assistant without data sync
        logger.info(f"Creating Pinecone assistant for store {store_id} (without data sync)")
        assistant_success = setup_pinecone_assistant_no_sync(db, store_id, user_id)

        if assistant_success:
            # Update store status to active
            StoreCRUD.update_store(db, store_id, status='active')
            logger.info(f"Post-OAuth assistant setup completed successfully for store {store_id}")
        else:
            logger.warning(f"Assistant setup failed for store {store_id}")
            StoreCRUD.update_store(db, store_id, status='active')  # Still mark as active, user can sync manually

    except Exception as e:
        logger.error(f"Post-OAuth assistant setup failed for store {store_id}: {str(e)}", exc_info=True)
        try:
            StoreCRUD.update_store(db, store_id, status='active')  # Mark as active, user can sync manually
        except Exception as status_error:
            logger.error(f"Failed to update store status: {status_error}")

def setup_pinecone_assistant(db: Session, store_id: int, user_id: int) -> bool:
    """
    Set up Pinecone assistant for the store (DEPRECATED - includes automatic sync)

    This function is deprecated for new store connections.
    Use setup_pinecone_assistant_no_sync() instead.

    Args:
        db: Database session
        store_id: ID of the store
        user_id: ID of the user

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get store details
        store = StoreCRUD.get_store_by_id(db, store_id)
        if not store:
            logger.error(f"Store {store_id} not found for assistant setup")
            return False

        # Check if assistant already exists
        existing_assistant = AssistantCRUD.get_assistant_by_store(db, store_id)

        if existing_assistant:
            logger.info(f"Assistant already exists for store {store_id}, syncing data...")
            # Sync data for existing assistant
            try:
                sync_result = multi_store_manager.sync_store_data(db, store_id, user_id, force=True)

                if sync_result.success:
                    logger.info(f"Assistant data synced successfully for store {store_id}")
                    return True
                else:
                    logger.error(f"Failed to sync assistant data for store {store_id}: {sync_result.message}")
                    AssistantCRUD.update_assistant(db, existing_assistant.id, status='error')
                    return False
            except Exception as e:
                logger.error(f"Exception during assistant data sync for store {store_id}: {str(e)}")
                AssistantCRUD.update_assistant(db, existing_assistant.id, status='error')
                return False

        else:
            logger.info(f"Creating new assistant for store {store_id}")
            # Create new assistant using the manager
            try:
                assistant = multi_store_manager.create_assistant_for_store(db, store)

                if assistant:
                    logger.info(f"Assistant created successfully for store {store_id}: {assistant.assistant_name}")

                    # Sync initial data
                    sync_result = multi_store_manager.sync_store_data(db, store_id, user_id, force=True)

                    if sync_result.success:
                        logger.info(f"Initial data sync completed for store {store_id}")
                        return True
                    else:
                        logger.warning(f"Assistant created but initial sync failed for store {store_id}: {sync_result.message}")
                        # Still return True since assistant was created
                        return True
                else:
                    logger.error(f"Failed to create assistant for store {store_id}")
                    return False

            except Exception as e:
                logger.error(f"Exception during assistant creation for store {store_id}: {str(e)}")
                return False

    except Exception as e:
        logger.error(f"Assistant setup failed for store {store_id}: {str(e)}", exc_info=True)
        return False

def setup_pinecone_assistant_no_sync(db: Session, store_id: int, user_id: int) -> bool:
    """
    Set up Pinecone assistant for the store WITHOUT automatic data sync

    Args:
        db: Database session
        store_id: ID of the store
        user_id: ID of the user

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get store details
        store = StoreCRUD.get_store_by_id(db, store_id)
        if not store:
            logger.error(f"Store {store_id} not found for assistant setup")
            return False

        # Check if assistant already exists
        existing_assistant = AssistantCRUD.get_assistant_by_store(db, store_id)

        if existing_assistant:
            logger.info(f"Assistant already exists for store {store_id} (no sync performed)")
            # Just mark as active, no sync
            AssistantCRUD.update_assistant(db, existing_assistant.id, status='active')
            return True

        else:
            logger.info(f"Creating new assistant for store {store_id} (no sync)")
            # Create new assistant using the manager
            try:
                assistant = multi_store_manager.create_assistant_for_store(db, store)

                if assistant:
                    logger.info(f"Assistant created successfully for store {store_id}: {assistant.assistant_name}")
                    # No data sync - just mark as active
                    return True
                else:
                    logger.error(f"Failed to create assistant for store {store_id}")
                    return False

            except Exception as e:
                logger.error(f"Exception during assistant creation for store {store_id}: {str(e)}")
                return False

    except Exception as e:
        logger.error(f"Assistant setup (no sync) failed for store {store_id}: {str(e)}", exc_info=True)
        return False

def cleanup_failed_setup(db: Session, store_id: int):
    """
    Clean up resources for failed setup

    Args:
        db: Database session
        store_id: ID of the store
    """
    try:
        logger.info(f"Cleaning up failed setup for store {store_id}")

        # Update store status to error
        StoreCRUD.update_store(db, store_id, status='error')

        # Mark any existing assistant as error
        assistant = AssistantCRUD.get_assistant_by_store(db, store_id)
        if assistant:
            AssistantCRUD.update_assistant(db, assistant.id, status='error')

        logger.info(f"Cleanup completed for store {store_id}")

    except Exception as e:
        logger.error(f"Cleanup failed for store {store_id}: {str(e)}", exc_info=True)

def get_setup_status(db: Session, store_id: int) -> dict:
    """
    Get the current setup status for a store

    Args:
        db: Database session
        store_id: ID of the store

    Returns:
        dict: Status information
    """
    try:
        store = StoreCRUD.get_store_by_id(db, store_id)
        if not store:
            return {"error": "Store not found"}

        assistant = AssistantCRUD.get_assistant_by_store(db, store_id)

        return {
            "store_id": store_id,
            "store_status": store.status,
            "store_name": store.store_name,
            "assistant_exists": assistant is not None,
            "assistant_status": assistant.status if assistant else None,
            "assistant_name": assistant.assistant_name if assistant else None,
            "last_sync": store.last_sync.isoformat() if store.last_sync else None
        }

    except Exception as e:
        logger.error(f"Failed to get setup status for store {store_id}: {str(e)}")
        return {"error": str(e)}
