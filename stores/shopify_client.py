import os
import requests
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ShopifyAPIClient:
    """Shopify API client for making authenticated requests"""

    def __init__(self, shop_url: str, access_token: str):
        # Validate and normalize the shop URL
        self.shop_url = validate_shopify_domain(shop_url)
        self.access_token = access_token
        self.api_version = os.getenv("SHOPIFY_API_VERSION", "2025-01")
        self.base_url = f"https://{self.shop_url}/admin/api/{self.api_version}"

        self.headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json'
        }

    # test_connection method removed - OAuth-only approach

    def get_shop_info_graphql(self) -> Dict[str, Any]:
        """Get shop information using GraphQL"""
        query = """
        {
          shop {
            id
            name
            email
            myshopifyDomain
            primaryDomain { host }
            currencyCode
            ianaTimezone
            billingAddress {
              phone
              city
              country
              countryCodeV2
              latitude
              longitude
            }
            createdAt
          }
        }
        """

        return self._make_graphql_request(query)

    def get_products_count(self) -> int:
        """Get total number of products using GraphQL"""
        try:
            query = """
            {
              productsCount {
                count
              }
            }
            """
            result = self._make_graphql_request(query)
            return result.get('productsCount', {}).get('count', 0)
        except:
            return 0

    def get_orders_count(self) -> int:
        """Get total number of orders using GraphQL"""
        try:
            query = """
            {
              ordersCount {
                count
              }
            }
            """
            result = self._make_graphql_request(query)
            return result.get('ordersCount', {}).get('count', 0)
        except:
            return 0

    def get_customers_count(self) -> int:
        """Get total number of customers using GraphQL"""
        try:
            query = """
            {
              customersCount {
                count
              }
            }
            """
            result = self._make_graphql_request(query)
            return result.get('customersCount', {}).get('count', 0)
        except:
            return 0

    def get_latest_order(self) -> Optional[Dict[str, Any]]:
        """Get the latest order using GraphQL"""
        try:
            query = """
            {
              orders(first: 1, sortKey: CREATED_AT, reverse: true) {
                edges {
                  node {
                    id
                    name
                    createdAt
                    totalPriceSet {
                      shopMoney {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
            """
            result = self._make_graphql_request(query)
            orders = result.get('orders', {}).get('edges', [])
            if orders:
                return orders[0]['node']
            return None
        except:
            return None

    def _make_graphql_request(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Make GraphQL request to Shopify"""
        url = f"{self.base_url}/graphql.json"

        payload = {'query': query}
        if variables:
            payload['variables'] = variables

        try:
            response = requests.post(url, json=payload, headers=self.headers, timeout=30)

            # Check HTTP status
            if response.status_code == 401:
                raise Exception("Invalid access token - check your Shopify private app credentials")
            elif response.status_code == 404:
                raise Exception("Store not found - check your shop URL")
            elif response.status_code == 403:
                raise Exception("Access forbidden - check your app permissions")

            response.raise_for_status()

            result = response.json()

            # Check for GraphQL errors
            if 'errors' in result:
                error_messages = []
                for error in result['errors']:
                    error_messages.append(error.get('message', 'Unknown GraphQL error'))
                raise Exception(f"GraphQL errors: {'; '.join(error_messages)}")

            # Return the data portion
            return result.get('data', {})

        except requests.exceptions.ConnectionError:
            raise Exception("Connection error - check your internet connection and shop URL")
        except requests.exceptions.Timeout:
            raise Exception("Request timeout - Shopify API is not responding")
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {str(e)}")
        except ValueError as e:
            raise Exception(f"Invalid JSON response: {str(e)}")



def validate_shopify_domain(domain: str) -> str:
    """Validate and normalize Shopify domain"""
    domain = domain.strip().lower()

    # Remove protocol if present
    if domain.startswith('http://') or domain.startswith('https://'):
        domain = domain.split('://', 1)[1]

    # Remove trailing slash
    domain = domain.rstrip('/')

    # Ensure it ends with .myshopify.com
    if not domain.endswith('.myshopify.com'):
        if '.' not in domain:
            domain = f"{domain}.myshopify.com"
        else:
            raise ValueError('Invalid Shopify domain format')

    return domain

def extract_shop_name(shop_url: str) -> str:
    """Extract shop name from Shopify URL - needed for assistant naming"""
    shop_url = validate_shopify_domain(shop_url)
    return shop_url.replace('.myshopify.com', '')
