from pydantic import BaseModel, field_validator
from typing import Optional, List
from datetime import datetime

# Manual connection models removed - OAuth-only approach

class StoreUpdate(BaseModel):
    """Model for updating store information"""
    store_name: Optional[str] = None
    status: Optional[str] = None

    @field_validator('status')
    @classmethod
    def validate_status(cls, v: Optional[str]) -> Optional[str]:
        """Validate store status"""
        if v is not None:
            allowed_statuses = ['active', 'inactive', 'error', 'syncing']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {", ".join(allowed_statuses)}')
        return v

class StoreResponse(BaseModel):
    """Model for store response"""
    id: int
    user_id: int
    shopify_store_id: str
    guid: str
    shop_url: str
    store_name: Optional[str]
    domain: Optional[str]
    myshopify_domain: Optional[str]
    email: Optional[str]

    # Status and metadata
    status: str
    is_deleted: bool
    created_at: datetime
    updated_at: Optional[datetime]
    last_sync: Optional[datetime]

    class Config:
        from_attributes = True

class StoreStats(BaseModel):
    """Model for store statistics"""
    total_products: int = 0
    total_orders: int = 0
    total_customers: int = 0
    last_order_date: Optional[datetime] = None
    store_created_date: Optional[datetime] = None

class StoreWithStats(StoreResponse):
    """Store response with statistics"""
    stats: Optional[StoreStats] = None
    assistant_status: Optional[str] = None
    assistant_name: Optional[str] = None

class BulkStoreOperation(BaseModel):
    """Model for bulk store operations"""
    store_ids: List[int]
    operation: str  # 'sync', 'activate', 'deactivate', 'delete'

    @field_validator('operation')
    @classmethod
    def validate_operation(cls, v: str) -> str:
        """Validate bulk operation type"""
        allowed_operations = ['sync', 'activate', 'deactivate', 'delete']
        if v not in allowed_operations:
            raise ValueError(f'Operation must be one of: {", ".join(allowed_operations)}')
        return v

class StoreSyncRequest(BaseModel):
    """Model for store sync requests"""
    force_sync: bool = False

class StoreSearchFilter(BaseModel):
    """Model for store search and filtering"""
    status: Optional[str] = None
    country: Optional[str] = None
    has_assistant: Optional[bool] = None
    last_sync_before: Optional[datetime] = None
    last_sync_after: Optional[datetime] = None
    search_term: Optional[str] = None  # Search in store name, domain, email