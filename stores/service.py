from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
import requests
import uuid
import logging

from database.models import Store
from database.crud import StoreCRUD, AssistantCRUD
from .shopify_client import ShopifyAPIClient
from .models import StoreUpdate, StoreStats
from config import settings, OAuthConfig

class StoreService:
    """Service class for store management operations"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def generate_oauth_url(self, shop: str, state: str = None) -> str:
        """Generate OAuth authorization URL for a shop"""
        if not shop.endswith(".myshopify.com"):
            raise ValueError("Invalid Shopify domain format")

        oauth_url = settings.get_oauth_install_url(shop)

        # Add state parameter if provided
        if state:
            oauth_url += f"&state={state}"

        return oauth_url

    def exchange_code_for_token(self, shop: str, code: str) -> str:
        """Exchange OAuth authorization code for permanent access token"""
        url = OAuthConfig.get_token_exchange_url(shop)
        payload = OAuthConfig.get_token_exchange_payload(code)

        try:
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()
            access_token = result.get("access_token")

            if not access_token:
                raise ValueError("No access token received from Shopify")

            self.logger.info(f"Successfully exchanged OAuth code for token for shop: {shop}")
            return access_token

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to exchange OAuth code for shop {shop}: {e}")
            raise ValueError(f"Failed to exchange authorization code: {str(e)}")

    def fetch_shop_data_from_graphql(self, shop: str, token: str) -> dict:
        """Fetch shop information using Shopify GraphQL API"""
        url = settings.get_shopify_graphql_url(shop)
        headers = OAuthConfig.get_graphql_headers(token)

        query = {
            "query": """
            {
              shop {
                id
                name
                email
                myshopifyDomain
                shopOwnerName
                primaryDomain { host }
                currencyCode
                ianaTimezone
                billingAddress {
                  phone
                  city
                  country
                  countryCodeV2
                  province
                  provinceCode
                  latitude
                  longitude
                  zip
                }
                currencyFormats {
                  moneyFormat
                }
                weightUnit
                timezoneAbbreviation
              }
            }
            """
        }

        try:
            self.logger.info(f"Fetching shop data from GraphQL for shop: {shop}")
            response = requests.post(url, json=query, headers=headers, timeout=30)
            response.raise_for_status()

            result = response.json()

            # Check for GraphQL errors
            if 'errors' in result:
                error_messages = [error.get('message', 'Unknown error') for error in result['errors']]
                raise ValueError(f"GraphQL errors: {'; '.join(error_messages)}")

            shop_data = result.get("data", {}).get("shop", {})
            if not shop_data:
                raise ValueError("No shop data returned from Shopify API")

            self.logger.info(f"Successfully fetched shop data for shop: {shop}")
            return shop_data

        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP request error while fetching shop data for {shop}: {e}")
            raise ValueError(f"Failed to fetch shop data: {str(e)}")

    def create_store_from_oauth(self, db: Session, user_id: int, shop_data: dict, access_token: str) -> Store:
        """Create a new store connection from OAuth flow"""
        # Extract Shopify store ID from GraphQL format: "gid://shopify/Shop/123456"
        shopify_store_id = shop_data['id'].split('/')[-1] if shop_data.get('id') else str(uuid.uuid4())

        # Check if this user already has this store connected
        existing_user_store = StoreCRUD.get_store_by_user_and_shopify_id(db, user_id, shopify_store_id)
        if existing_user_store:
            # Update existing store with new token and data
            self.logger.info(f"Updating existing store {shopify_store_id} for user {user_id}")
            return self._update_existing_store_from_oauth(db, existing_user_store, shop_data, access_token)

        # Extract data from GraphQL response
        store_name = shop_data.get('name')
        email = shop_data.get('email')
        domain = shop_data.get('primaryDomain', {}).get('host') if shop_data.get('primaryDomain') else None
        myshopify_domain = shop_data.get('myshopifyDomain')
        shop_url = myshopify_domain  # Use myshopify domain as shop URL

        # Create store record
        store = StoreCRUD.create_store(
            db=db,
            user_id=user_id,
            shopify_store_id=shopify_store_id,
            shop_url=shop_url,
            access_token=access_token,
            store_name=store_name,
            domain=domain,
            myshopify_domain=myshopify_domain,
            email=email
        )

        # Automatically create assistant for the new store
        try:
            from assistants.multi_store_assistant import multi_store_manager
            assistant = multi_store_manager.create_assistant_for_store(db, store)

            if assistant:
                self.logger.info(f"Assistant created successfully for store {store.id}")
            else:
                self.logger.warning(f"Failed to create assistant for store {store.id}")
        except Exception as e:
            # Log the error but don't fail store creation
            self.logger.error(f"Failed to create assistant for store {store.id}: {str(e)}")

        return store

    def _update_existing_store_from_oauth(self, db: Session, store: Store, shop_data: dict, access_token: str) -> Store:
        """Update existing store with new OAuth data"""
        # Update store with new token and fresh data
        update_data = {
            'access_token_encrypted': StoreCRUD.encode_token(access_token),
            'store_name': shop_data.get('name'),
            'email': shop_data.get('email'),
            'domain': shop_data.get('primaryDomain', {}).get('host') if shop_data.get('primaryDomain') else None,
            'myshopify_domain': shop_data.get('myshopifyDomain'),
            'status': 'active',
            'updated_at': datetime.now(timezone.utc)
        }

        for key, value in update_data.items():
            if hasattr(store, key) and value is not None:
                setattr(store, key, value)

        db.commit()
        db.refresh(store)

        self.logger.info(f"Updated existing store {store.id} with new OAuth data")
        return store

    def save_or_update_store_from_oauth(self, db: Session, user_id: int, shop_data: dict, access_token: str) -> Store:
        """Save or update store information from OAuth flow"""
        myshopify_domain = shop_data.get("myshopifyDomain")
        if not myshopify_domain:
            raise ValueError("Shop data is missing 'myshopifyDomain'. Cannot save or update store.")

        try:
            self.logger.info(f"Checking for existing store record for {myshopify_domain}")

            # Check if store exists for this user and domain
            existing_store = StoreCRUD.get_store_by_user_and_domain(db, user_id, myshopify_domain)

            if existing_store:
                # Update existing store
                self.logger.info(f"Updating existing store record for {myshopify_domain}")
                return self._update_existing_store_from_oauth(db, existing_store, shop_data, access_token)
            else:
                # Create new store
                self.logger.info(f"Creating new store record for {myshopify_domain}")
                return self.create_store_from_oauth(db, user_id, shop_data, access_token)

        except Exception as e:
            self.logger.error(f"Error while saving or updating store for {myshopify_domain}: {e}")
            raise ValueError(f"Failed to save or update store: {e}")

    def get_user_stores(self, db: Session, user_id: int, include_stats: bool = False) -> List[Store]:
        """Get all stores for a user"""
        stores = StoreCRUD.get_stores_by_user(db, user_id)

        if include_stats:
            for store in stores:
                store.stats = self.get_store_stats(store)
                # Get assistant info
                assistant = AssistantCRUD.get_assistant_by_store(db, store.id)
                if assistant:
                    store.assistant_status = assistant.status
                    store.assistant_name = assistant.assistant_name

        return stores

    def get_store_by_id(self, db: Session, store_id: int, user_id: int) -> Optional[Store]:
        """Get store by ID (ensuring user ownership)"""
        store = StoreCRUD.get_store_by_id(db, store_id)
        if store and store.user_id == user_id:
            return store
        return None

    def update_store(self, db: Session, store_id: int, user_id: int, update_data: StoreUpdate) -> Optional[Store]:
        """Update store information"""
        store = self.get_store_by_id(db, store_id, user_id)
        if not store:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)
        return StoreCRUD.update_store(db, store_id, **update_dict)

    def delete_store(self, db: Session, store_id: int, user_id: int, soft_delete: bool = True) -> bool:
        """
        Delete store with shared Pinecone assistant logic.

        - Always deletes the user's store record
        - Only deletes Pinecone assistant if no other users have the same store
        """
        store = self.get_store_by_id(db, store_id, user_id)
        if not store:
            return False

        # Get store details before deletion
        shopify_store_id = store.shopify_store_id

        # Check if other users have the same Shopify store
        has_other_users = StoreCRUD.check_if_store_has_other_users(
            db, shopify_store_id, exclude_user_id=user_id
        )

        # Get assistant before store deletion
        assistant = AssistantCRUD.get_assistant_by_store(db, store_id)

        # Always delete the user's store record first
        store_deleted = StoreCRUD.delete_store(db, store_id, soft_delete)
        if not store_deleted:
            return False

        # Handle assistant deletion based on shared usage
        if assistant:
            if has_other_users:
                # Other users have this store - only delete the database assistant record
                # but keep the Pinecone assistant instance
                print(f"Store {shopify_store_id} has other users - keeping Pinecone assistant, deleting DB record only")
                db.delete(assistant)
                db.commit()

                # Remove from cache if present
                from assistants.multi_store_assistant import multi_store_manager
                if store_id in multi_store_manager.active_assistants:
                    del multi_store_manager.active_assistants[store_id]
                if store_id in multi_store_manager.assistant_cache_timestamps:
                    del multi_store_manager.assistant_cache_timestamps[store_id]
            else:
                # No other users have this store - delete both DB record and Pinecone assistant
                print(f"Store {shopify_store_id} has no other users - deleting Pinecone assistant completely")
                try:
                    # Delete the Pinecone assistant instance
                    from pinecone_assistant import PineconeAssistant
                    pinecone_assistant = PineconeAssistant(assistant_name=assistant.assistant_name)
                    pinecone_assistant.delete_assistant()
                    print(f"Successfully deleted Pinecone assistant: {assistant.assistant_name}")
                except Exception as e:
                    print(f"Warning: Failed to delete Pinecone assistant {assistant.assistant_name}: {e}")

                # Delete the database record
                db.delete(assistant)
                db.commit()

                # Remove from cache if present
                from assistants.multi_store_assistant import multi_store_manager
                if store_id in multi_store_manager.active_assistants:
                    del multi_store_manager.active_assistants[store_id]
                if store_id in multi_store_manager.assistant_cache_timestamps:
                    del multi_store_manager.assistant_cache_timestamps[store_id]

        return True

    def sync_store_data(self, db: Session, store_id: int, user_id: int, force: bool = False) -> bool:
        """Sync store data from Shopify and update Pinecone assistant"""
        store = self.get_store_by_id(db, store_id, user_id)
        if not store:
            return False

        try:
            print(f"DEBUG: Starting sync for store {store_id}...")

            # Decode access token
            access_token = StoreCRUD.decode_token(store.access_token_encrypted)
            client = ShopifyAPIClient(store.shop_url, access_token)

            # Get updated shop info using GraphQL
            shop_info = client.get_shop_info_graphql()
            if 'shop' not in shop_info:
                print("ERROR: Failed to get shop info from GraphQL")
                return False

            shop_data = shop_info['shop']

            # Update store information using GraphQL field names
            update_data = {
                'store_name': shop_data.get('name'),
                'email': shop_data.get('email'),
                'last_sync': datetime.now(timezone.utc)
            }

            StoreCRUD.update_store(db, store_id, **update_data)
            print("DEBUG: Store information updated successfully")

            # Now sync the data to Pinecone assistant
            print("DEBUG: Starting Pinecone assistant data sync...")
            from assistants.multi_store_assistant import multi_store_manager
            sync_result = multi_store_manager.sync_store_data(db, store_id, user_id, force=force)

            if sync_result.success:
                print("DEBUG: Pinecone assistant data sync completed successfully")
                return True
            else:
                print(f"ERROR: Pinecone assistant data sync failed: {sync_result.message}")
                return False

        except Exception as e:
            print(f"ERROR: Store sync failed: {str(e)}")
            import traceback
            traceback.print_exc()
            # Update store status to error
            StoreCRUD.update_store(db, store_id, status='error', last_sync=datetime.now(timezone.utc))
            return False

    def get_store_stats(self, store: Store) -> StoreStats:
        """Get store statistics from Shopify"""
        try:
            access_token = StoreCRUD.decode_token(store.access_token_encrypted)
            client = ShopifyAPIClient(store.shop_url, access_token)

            # Get counts
            products_count = client.get_products_count()
            orders_count = client.get_orders_count()
            customers_count = client.get_customers_count()

            # Get latest order
            latest_order = client.get_latest_order()
            last_order_date = None
            if latest_order:
                last_order_date = datetime.fromisoformat(
                    latest_order['createdAt'].replace('Z', '+00:00')
                )

            return StoreStats(
                total_products=products_count,
                total_orders=orders_count,
                total_customers=customers_count,
                last_order_date=last_order_date,
                store_created_date=store.created_at
            )

        except Exception as e:
            print(f"Error getting store stats: {e}")
            return StoreStats()

    def bulk_operation(self, db: Session, user_id: int, store_ids: List[int], operation: str) -> Dict[str, Any]:
        """Perform bulk operations on stores"""
        results = {
            'success': [],
            'failed': [],
            'total': len(store_ids)
        }

        for store_id in store_ids:
            try:
                if operation == 'sync':
                    success = self.sync_store_data(db, store_id, user_id, force=False)
                elif operation == 'activate':
                    store = self.update_store(db, store_id, user_id, StoreUpdate(status='active'))
                    success = store is not None
                elif operation == 'deactivate':
                    store = self.update_store(db, store_id, user_id, StoreUpdate(status='inactive'))
                    success = store is not None
                elif operation == 'delete':
                    success = self.delete_store(db, store_id, user_id)
                else:
                    success = False

                if success:
                    results['success'].append(store_id)
                else:
                    results['failed'].append(store_id)

            except Exception:
                results['failed'].append(store_id)

        return results
