<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Management - Pinecone E-commerce Assistant</title>
    <link href="/static/css/styles.css" rel="stylesheet">
    <style>
        .stores-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .stores-header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn:disabled {
            background-color: #6c757d !important;
            color: #fff !important;
            cursor: not-allowed !important;
            opacity: 0.6;
        }
        .btn:disabled:hover {
            background-color: #6c757d !important;
            transform: none !important;
        }
        .stores-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .store-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        .store-card:hover {
            transform: translateY(-2px);
        }
        .store-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .store-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0 0 5px 0;
            color: #333;
        }
        .store-url {
            color: #666;
            font-size: 0.9rem;
        }
        .store-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-top: 10px;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-syncing {
            background-color: #fff3cd;
            color: #856404;
        }
        .store-stats {
            padding: 15px 20px;
            background-color: #f8f9fa;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            text-align: center;
        }
        .stat-item {
            padding: 10px;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 0.8rem;
            color: #666;
            text-transform: uppercase;
        }
        .store-actions {
            padding: 15px 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .empty-state i {
            font-size: 64px;
            color: #ccc;
            margin-bottom: 20px;
        }
        .add-store-form {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: none;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        .form-actions {
            display: flex;
            gap: 10px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid #f5c6cb;
            display: none;
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid #c3e6cb;
            display: none;
        }
        .connection-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab-btn {
            background: none;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-size: 14px;
            color: #666;
            transition: all 0.3s ease;
        }
        .tab-btn.active {
            color: #007bff;
            border-bottom-color: #007bff;
            font-weight: bold;
        }
        .tab-btn:hover {
            color: #007bff;
            background-color: #f8f9fa;
        }
        .connection-form {
            transition: opacity 0.3s ease;
        }
        @media (max-width: 768px) {
            .stores-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            .stores-grid {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                grid-template-columns: 1fr;
            }
            .store-actions {
                flex-direction: column;
            }
        }

        /* Professional Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            animation: slideUp 0.3s ease-out;
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24px 32px;
            border-bottom: 1px solid #e5e7eb;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .modal-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .modal-icon {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-title h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .modal-body {
            padding: 32px;
        }

        .connection-steps {
            display: flex;
            gap: 24px;
            margin-bottom: 32px;
            padding: 24px;
            background: #f8fafc;
            border-radius: 12px;
        }

        .step {
            flex: 1;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            opacity: 0.5;
            transition: opacity 0.3s;
        }

        .step.active {
            opacity: 1;
        }

        .step-icon {
            width: 32px;
            height: 32px;
            background: #e5e7eb;
            color: #6b7280;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
        }

        .step.active .step-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .step-content h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .step-content p {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        .connection-form {
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
        }

        .input-container {
            position: relative;
            display: flex;
            align-items: center;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            transition: border-color 0.2s;
        }

        .input-container:focus-within {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-container input {
            flex: 1;
            padding: 12px 16px;
            border: none;
            outline: none;
            font-size: 16px;
            background: transparent;
        }

        .input-suffix {
            padding: 12px 16px;
            color: #6b7280;
            font-size: 16px;
            background: #f9fafb;
            border-left: 1px solid #e5e7eb;
        }

        .form-help {
            margin-top: 6px;
            font-size: 12px;
            color: #6b7280;
        }

        .validation-message {
            margin-top: 8px;
            font-size: 12px;
            font-weight: 500;
        }

        .validation-message.success {
            color: #059669;
        }

        .validation-message.error {
            color: #dc2626;
        }

        .progress-container {
            margin: 24px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
            animation: progressPulse 2s infinite;
        }

        .progress-text {
            font-size: 14px;
            color: #6b7280;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 32px;
        }

        .btn-spinner {
            animation: spin 1s linear infinite;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes progressPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Mobile responsiveness for modal */
        @media (max-width: 768px) {
            .modal-container {
                width: 95%;
                margin: 20px;
            }

            .modal-header {
                padding: 20px 24px;
            }

            .modal-body {
                padding: 24px;
            }

            .connection-steps {
                flex-direction: column;
                gap: 16px;
            }

            .step {
                flex-direction: row;
                align-items: center;
            }

            .modal-actions {
                flex-direction: column-reverse;
            }

            .modal-actions .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="stores-container">
        <!-- Header -->
        <div class="stores-header">
            <div>
                <h1>Store Management</h1>
                <p>Connect and manage your Shopify stores</p>
            </div>
            <div>
                <button id="add-store-btn" class="btn btn-primary">
                    Add Store
                </button>
                <a href="/dashboard" class="btn btn-secondary">
                    Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Messages -->
        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <!-- Professional Store Connection Modal -->
        <div id="store-connection-modal" class="modal-overlay" style="display: none;">
            <div class="modal-container">
                <div class="modal-header">
                    <div class="modal-title">
                        <div class="modal-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <h3>Connect Your Shopify Store</h3>
                    </div>
                    <button type="button" id="close-modal-btn" class="modal-close">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>

                <div class="modal-body">
                    <div class="connection-steps">
                        <div class="step active" id="step-1">
                            <div class="step-icon">1</div>
                            <div class="step-content">
                                <h4>Enter Store Domain</h4>
                                <p>Provide your Shopify store's domain to begin the secure connection process.</p>
                            </div>
                        </div>
                        <div class="step" id="step-2">
                            <div class="step-icon">2</div>
                            <div class="step-content">
                                <h4>Authorize Connection</h4>
                                <p>You'll be redirected to Shopify to authorize our app's access to your store.</p>
                            </div>
                        </div>
                        <div class="step" id="step-3">
                            <div class="step-icon">3</div>
                            <div class="step-content">
                                <h4>Data Synchronization</h4>
                                <p>We'll automatically sync your store data and set up your AI assistant.</p>
                            </div>
                        </div>
                    </div>

                    <form id="store-connection-form" class="connection-form">
                        <div class="form-group">
                            <label for="shop-domain">Shopify Store Domain</label>
                            <div class="input-container">
                                <input
                                    type="text"
                                    id="shop-domain"
                                    name="shop_domain"
                                    placeholder="your-store-name"
                                    autocomplete="off"
                                    spellcheck="false"
                                >
                                <span class="input-suffix">.myshopify.com</span>
                            </div>
                            <div class="form-help">
                                Enter only your store name (e.g., "my-awesome-store" for my-awesome-store.myshopify.com)
                            </div>
                            <div id="domain-validation" class="validation-message"></div>
                        </div>

                        <div id="connection-progress" class="progress-container" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div class="progress-text">Connecting to Shopify...</div>
                        </div>

                        <div class="modal-actions">
                            <button type="button" id="cancel-connection-btn" class="btn btn-secondary">
                                Cancel
                            </button>
                            <button type="submit" id="connect-store-btn" class="btn btn-primary" disabled>
                                <span class="btn-text">Connect Store</span>
                                <span class="btn-spinner" style="display: none;">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48l2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48l2.83-2.83" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Stores Grid -->
        <div id="stores-grid" class="stores-grid">
            <!-- Stores will be loaded here -->
        </div>

        <!-- Empty State -->
        <div id="empty-state" class="empty-state" style="display: none;">
            <div style="font-size: 64px; margin-bottom: 20px;">🏪</div>
            <h3>No Stores Connected</h3>
            <p>Connect your first Shopify store to get started with AI-powered customer assistance.</p>
            <button onclick="showStoreConnectionModal()" class="btn btn-primary" style="margin-top: 20px;">
                Connect Your First Store
            </button>
        </div>
    </div>

    <script src="/static/js/auth.js"></script>
    <script src="/static/js/stores.js"></script>
</body>
</html>
