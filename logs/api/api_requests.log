{"timestamp": "2025-05-27T08:53:54.906237+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/123/sync - 200 (1.234s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/123/sync", "status_code": 200, "duration_ms": 1234.0}, "user_id": 456, "request_id": "req-12345"}
{"timestamp": "2025-05-27T09:41:31.538496+00:00", "level": "INFO", "logger": "api", "message": "GET /profile - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/profile", "status_code": 200, "duration_ms": 6.02}, "user_id": null, "request_id": "5e010312-8095-4655-aa68-502fb29c1ecc"}
{"timestamp": "2025-05-27T09:41:31.552032+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/style.css - 404 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/style.css", "status_code": 404, "duration_ms": 3.84}, "user_id": null, "request_id": "7719fa06-c259-41d9-aeb1-3881866e2e7c"}
{"timestamp": "2025-05-27T09:41:31.576291+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 9.46}, "user_id": null, "request_id": "e33eb5a3-8258-4465-a66f-68dd8d607510"}
{"timestamp": "2025-05-27T09:41:31.581292+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 1.4}, "user_id": null, "request_id": "2a354840-d343-4e0d-afe7-0f819485e01f"}
{"timestamp": "2025-05-27T09:41:32.831615+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.4}, "user_id": null, "request_id": "d472514f-0b6c-4bf2-afc9-5d658b3c0d42"}
{"timestamp": "2025-05-27T09:41:32.851164+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 4.44}, "user_id": null, "request_id": "57ad9bc6-6f87-4aa6-9c77-e081da73fed8"}
{"timestamp": "2025-05-27T09:41:32.851521+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 4.2}, "user_id": null, "request_id": "d3968d42-1280-4234-9985-ea512c942e6d"}
{"timestamp": "2025-05-27T09:41:32.852369+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 4.07}, "user_id": null, "request_id": "15f2c542-c163-42b9-92df-d2dd04ac4eb4"}
{"timestamp": "2025-05-27T09:41:32.860386+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.53}, "user_id": null, "request_id": "a7906fff-8184-451b-aa87-d06ee16bafc6"}
{"timestamp": "2025-05-27T09:41:34.457027+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.249s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 248.58}, "user_id": null, "request_id": "779afedb-5af0-44d9-a7ff-a75977d4e45c"}
{"timestamp": "2025-05-27T09:41:35.466474+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.04}, "user_id": null, "request_id": "6c19e893-9721-44a2-bda0-b34890a01378"}
{"timestamp": "2025-05-27T09:41:38.548618+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.072s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3072.36}, "user_id": null, "request_id": "bffb0413-42a5-4a0b-a86a-e9436b759091"}
{"timestamp": "2025-05-27T09:41:38.553775+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.26}, "user_id": null, "request_id": "f437bfb6-e58d-41c9-89bc-2524c5e28986"}
{"timestamp": "2025-05-27T09:41:38.569937+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.023s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 22.6}, "user_id": null, "request_id": "fa7ef40e-3fc8-48f5-8dbe-48ec3b498c08"}
{"timestamp": "2025-05-27T09:41:38.570355+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.023s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 22.95}, "user_id": null, "request_id": "babd0e9d-2855-42d9-98f0-94d69ae73ee5"}
{"timestamp": "2025-05-27T09:41:38.583198+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 4.68}, "user_id": null, "request_id": "df11be15-98e6-4a62-b426-cfc83b90b7f1"}
{"timestamp": "2025-05-27T09:41:41.706501+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.121s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3120.82}, "user_id": null, "request_id": "51eafdd6-4d90-4315-a074-590c32359e53"}
{"timestamp": "2025-05-27T09:43:20.157508+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/7/sync - 200 (95.599s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/7/sync", "status_code": 200, "duration_ms": 95598.98}, "user_id": null, "request_id": "86bb35dd-752b-452e-b765-8598de814de2"}
{"timestamp": "2025-05-27T09:43:20.165198+00:00", "level": "INFO", "logger": "api", "message": "GET /sync_demo - 404 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/sync_demo", "status_code": 404, "duration_ms": 6.64}, "user_id": null, "request_id": "7bf8c1fb-19fa-406d-81fa-9df172fe4058"}
{"timestamp": "2025-05-27T09:43:20.165935+00:00", "level": "INFO", "logger": "api", "message": "GET /sync-demo - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/sync-demo", "status_code": 200, "duration_ms": 6.71}, "user_id": null, "request_id": "2c50d5d9-7d4a-4df9-8540-90f4c2134bd0"}
{"timestamp": "2025-05-27T09:43:20.166534+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 4.25}, "user_id": null, "request_id": "c6d4f572-dcdf-424e-9f68-74aeefc3496f"}
{"timestamp": "2025-05-27T09:43:23.071395+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.904s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2903.77}, "user_id": null, "request_id": "33f7256e-ee52-4a91-accc-3df0fae8b195"}
{"timestamp": "2025-05-27T09:43:23.073530+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/sync-manager.js - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/sync-manager.js", "status_code": 200, "duration_ms": 2.97}, "user_id": null, "request_id": "ad04e60b-ab14-49d7-ae09-66c3acbbfe76"}
{"timestamp": "2025-05-27T09:43:39.232474+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/123/sync - 422 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/123/sync", "status_code": 422, "duration_ms": 5.81}, "user_id": null, "request_id": "c230ca8b-80e2-4e40-b08e-ceb33acfb5ce"}
{"timestamp": "2025-05-27T09:47:45.008728+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 13.7}, "user_id": null, "request_id": "e89d2047-0c82-4d31-92cb-93d083357ddd"}
{"timestamp": "2025-05-27T09:47:47.153244+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 5.12}, "user_id": null, "request_id": "28786ea6-4225-4a41-a372-df426be9d216"}
{"timestamp": "2025-05-27T09:47:50.191910+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3016.02}, "user_id": null, "request_id": "e31486a9-792d-40a2-8463-a8b7b9155e1c"}
{"timestamp": "2025-05-27T09:47:50.192692+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (3.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 3015.71}, "user_id": null, "request_id": "44481543-9b7c-402f-a915-b2353eebfa05"}
{"timestamp": "2025-05-27T09:47:50.192932+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (3.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 3015.42}, "user_id": null, "request_id": "78c42c7d-d109-4d4d-be47-2b9d3ddaedf0"}
{"timestamp": "2025-05-27T09:47:50.783053+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 4.2}, "user_id": null, "request_id": "ee951289-5e83-4c5f-bbf0-12cd52bb2725"}
{"timestamp": "2025-05-27T09:47:53.290975+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.496s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2496.1}, "user_id": null, "request_id": "d573fe70-5e3f-42db-b3d3-018b90905460"}
{"timestamp": "2025-05-27T09:48:20.281572+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/7/sync - 200 (24.236s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/7/sync", "status_code": 200, "duration_ms": 24235.58}, "user_id": null, "request_id": "baa19ab6-d7e0-43ea-a59f-98cdc016296b"}
{"timestamp": "2025-05-27T09:48:22.625425+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.341s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2341.19}, "user_id": null, "request_id": "f5f2ffff-8e6c-45bf-a293-fb41a7c2c2d9"}
{"timestamp": "2025-05-27T09:51:14.781338+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.13}, "user_id": null, "request_id": "42fe3026-77b6-4bfa-88af-62f9e113890d"}
{"timestamp": "2025-05-27T09:51:17.513855+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.718s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2718.17}, "user_id": null, "request_id": "f47af818-9951-4869-b687-24db50c51223"}
{"timestamp": "2025-05-27T09:51:17.517568+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.721s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2720.54}, "user_id": null, "request_id": "90e83c13-8fcc-4729-a1cc-2b7dda1840ad"}
{"timestamp": "2025-05-27T09:51:17.517878+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.720s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2719.94}, "user_id": null, "request_id": "13f90447-8de4-49f7-b926-a8a6dd069410"}
{"timestamp": "2025-05-27T09:51:17.518396+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.72}, "user_id": null, "request_id": "0b4fd6f2-62d0-479a-8e5e-d4f350e10ea5"}
{"timestamp": "2025-05-27T09:51:21.193950+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.232s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 232.29}, "user_id": null, "request_id": "29d7874e-25ad-4841-9db1-497523c37b24"}
{"timestamp": "2025-05-27T09:51:22.202107+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.01}, "user_id": null, "request_id": "8b1fb227-8e72-4258-9f30-c43f1426469c"}
{"timestamp": "2025-05-27T09:51:24.938410+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.725s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2724.8}, "user_id": null, "request_id": "a7d546d0-66d3-4285-8b7f-b7be43434e34"}
{"timestamp": "2025-05-27T09:51:24.939036+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.724s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2724.44}, "user_id": null, "request_id": "8dc88d15-7058-43f8-b7f1-116edbaad911"}
{"timestamp": "2025-05-27T09:51:24.939204+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.724s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2724.49}, "user_id": null, "request_id": "b3179dee-40c0-48a9-aff6-c47fdd4b414e"}
{"timestamp": "2025-05-27T09:51:24.939575+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.91}, "user_id": null, "request_id": "accca7c5-d370-4064-b458-8f6cc462c3f7"}
{"timestamp": "2025-05-27T09:51:27.406248+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.456s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2455.61}, "user_id": null, "request_id": "dd6f3b33-3837-4f74-a403-1f1285cc73ad"}
{"timestamp": "2025-05-27T09:53:23.065592+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/12/sync - 200 (111.455s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/12/sync", "status_code": 200, "duration_ms": 111454.57}, "user_id": null, "request_id": "859d4a3a-227c-432b-b29c-762917195fd0"}
{"timestamp": "2025-05-27T09:53:25.516276+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.447s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2446.74}, "user_id": null, "request_id": "e265edef-b2ed-4256-8538-e9a6f0eb1899"}
{"timestamp": "2025-05-27T09:54:07.631829+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/12/sync - 200 (25.320s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/12/sync", "status_code": 200, "duration_ms": 25319.59}, "user_id": null, "request_id": "ff3d6d83-819e-4ee7-b5ae-fc04f203b194"}
{"timestamp": "2025-05-27T09:54:10.038083+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.404s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2403.67}, "user_id": null, "request_id": "da6e9046-c32d-4875-b4db-6de893f15c18"}
{"timestamp": "2025-05-28T05:17:30.520085+00:00", "level": "INFO", "logger": "api", "message": "GET /api/cron/status - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/api/cron/status", "status_code": 200, "duration_ms": 2.01}, "user_id": null, "request_id": "4234bc25-273e-48f5-b582-6a5c674126cb"}
{"timestamp": "2025-05-28T05:17:39.475955+00:00", "level": "INFO", "logger": "api", "message": "POST /api/cron/trigger - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/api/cron/trigger", "status_code": 200, "duration_ms": 8.41}, "user_id": null, "request_id": "a55bdaf8-d4db-440e-b1a0-b7f0f418e2b0"}
{"timestamp": "2025-05-28T05:33:14.150675+00:00", "level": "INFO", "logger": "api", "message": "GET /api/cron/status - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/api/cron/status", "status_code": 200, "duration_ms": 1.12}, "user_id": null, "request_id": "40e95062-32ba-4a4d-a8cb-1ca658404ccd"}
{"timestamp": "2025-05-28T06:04:40.143879+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 4.75}, "user_id": null, "request_id": "271d7783-52a1-45ef-b05f-512912478e17"}
{"timestamp": "2025-05-28T06:04:40.153882+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/styles.css - 304 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/styles.css", "status_code": 304, "duration_ms": 4.77}, "user_id": null, "request_id": "cee8a2b6-ff17-4b58-a275-2f78683646c3"}
{"timestamp": "2025-05-28T06:04:46.171512+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 2.0}, "user_id": null, "request_id": "3fc5c31c-190d-4197-b482-3ed118744ba9"}
{"timestamp": "2025-05-28T06:04:46.186787+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/auth.js - 304 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/auth.js", "status_code": 304, "duration_ms": 1.01}, "user_id": null, "request_id": "17922007-cfe2-407d-823c-5d5604e413aa"}
{"timestamp": "2025-05-28T06:04:46.206046+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 7.46}, "user_id": null, "request_id": "244082cf-14bb-4727-b0d4-59c3782f1e3c"}
{"timestamp": "2025-05-28T06:04:46.206183+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 7.56}, "user_id": null, "request_id": "afbf5020-18c4-41ab-be2c-c77e8325b635"}
{"timestamp": "2025-05-28T06:04:46.206268+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 7.57}, "user_id": null, "request_id": "5b0b0c9a-9e6e-4889-8ed5-f71f43d39a76"}
{"timestamp": "2025-05-28T06:04:46.211275+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.17}, "user_id": null, "request_id": "b11a58b6-7325-43e9-97ef-769662069039"}
{"timestamp": "2025-05-28T06:04:47.923334+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.242s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 241.69}, "user_id": null, "request_id": "fcd3ca30-54fa-4775-99a0-303eb4854cfd"}
{"timestamp": "2025-05-28T06:04:48.937684+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 2.24}, "user_id": null, "request_id": "b2e2cb1d-5897-4d47-a2ab-9c0f3c5c5c9b"}
{"timestamp": "2025-05-28T06:06:49.017691+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (120.068s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 120067.53}, "user_id": null, "request_id": "d40dca75-0402-4001-a199-4a4bd3d714cc"}
{"timestamp": "2025-05-28T06:06:49.031656+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (120.081s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 120080.78}, "user_id": null, "request_id": "f43238ce-eadd-4cb2-bf21-b00b6001b06f"}
{"timestamp": "2025-05-28T06:06:49.032597+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (120.078s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 120078.33}, "user_id": null, "request_id": "d19a03a3-0638-4488-a94c-011df526e2df"}
{"timestamp": "2025-05-28T06:06:49.033324+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 16.0}, "user_id": null, "request_id": "5ff6d687-cc8b-4747-9757-e68dacc207c1"}
{"timestamp": "2025-05-28T06:08:49.183160+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (120.062s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 120061.61}, "user_id": null, "request_id": "fb11dd7a-708f-4196-b4e3-ed7bba779efd"}
{"timestamp": "2025-05-28T06:08:49.184619+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (120.063s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 120062.77}, "user_id": null, "request_id": "63060eb6-5a5a-4764-a85c-407f810f2c4a"}
{"timestamp": "2025-05-28T06:08:49.184847+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (120.063s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 120062.94}, "user_id": null, "request_id": "0b2b30d4-6b98-44b2-bf9d-1d8c6894ab9c"}
{"timestamp": "2025-05-28T06:08:49.185337+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.11}, "user_id": null, "request_id": "dc98da1b-d401-4e02-b195-830b2571e27f"}
{"timestamp": "2025-05-28T06:14:53.451097+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.213s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 213.2}, "user_id": null, "request_id": "cfc8d7af-03df-426c-8b98-25a3223769b9"}
{"timestamp": "2025-05-28T06:14:54.537537+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.6}, "user_id": null, "request_id": "cb298193-28ff-41db-aa23-340d26f09665"}
{"timestamp": "2025-05-28T06:16:25.416549+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (90.857s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 90857.17}, "user_id": null, "request_id": "d5573ae4-2632-4bea-b198-fe31e82d0230"}
{"timestamp": "2025-05-28T06:16:25.434289+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (90.872s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 90872.3}, "user_id": null, "request_id": "d1cf36a5-ba62-434f-bde1-9a436e05da30"}
{"timestamp": "2025-05-28T06:16:25.434819+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (90.872s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 90871.97}, "user_id": null, "request_id": "912d9d99-4fe2-441f-9310-f6828139c343"}
{"timestamp": "2025-05-28T06:16:25.434915+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.019s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 18.98}, "user_id": null, "request_id": "826316c3-0c3a-4bab-86ce-f5bc842c6439"}
{"timestamp": "2025-05-28T06:16:25.435160+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 2.75}, "user_id": null, "request_id": "7105201c-e066-4671-b43e-1886e414af6c"}
{"timestamp": "2025-05-28T06:16:25.482298+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 28.6}, "user_id": null, "request_id": "1aad15b5-8192-48af-b35a-2c61d99c7ccf"}
{"timestamp": "2025-05-28T06:16:25.482438+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 28.7}, "user_id": null, "request_id": "a7e3a767-a9c6-448a-aa4c-d3770a6f5fd6"}
{"timestamp": "2025-05-28T06:16:25.482750+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.026s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 26.07}, "user_id": null, "request_id": "a46431e4-0f93-48b3-8fb9-34a9cd7a3955"}
{"timestamp": "2025-05-28T06:16:37.860680+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.41}, "user_id": null, "request_id": "80dda21c-a71b-4279-9ed1-ef02bfe1ff49"}
{"timestamp": "2025-05-28T06:16:37.876236+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 1.92}, "user_id": null, "request_id": "5f2faac4-e2cb-4276-88fa-4552e91ba4d6"}
{"timestamp": "2025-05-28T06:16:37.895398+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 16.6}, "user_id": null, "request_id": "3ca00a9b-763e-42c8-84f7-5e890fe7a831"}
{"timestamp": "2025-05-28T06:16:43.631991+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.97}, "user_id": null, "request_id": "dc551b42-475a-4ccc-bf04-fdbc2f764758"}
{"timestamp": "2025-05-28T06:16:43.659704+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 16.68}, "user_id": null, "request_id": "a49fe7e6-eec6-41d7-8f5b-bd8ed9cbe6ad"}
{"timestamp": "2025-05-28T06:16:53.111689+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8549255232, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 6.52}, "user_id": null, "request_id": "d1036348-cae0-473f-a7c5-c9aba19579a8"}
{"timestamp": "2025-05-28T06:41:16.138276+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 5.63}, "user_id": null, "request_id": "ef9355fc-919e-40e5-ab3e-d5070a91beda"}
{"timestamp": "2025-05-28T06:41:16.157540+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/styles.css - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/styles.css", "status_code": 200, "duration_ms": 5.19}, "user_id": null, "request_id": "86a1caf9-02d5-47a7-80a5-bdd2748d28c9"}
{"timestamp": "2025-05-28T06:41:16.199698+00:00", "level": "INFO", "logger": "api", "message": "GET /favicon.ico - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/favicon.ico", "status_code": 404, "duration_ms": 0.78}, "user_id": null, "request_id": "489bfe4d-76f2-4c2c-be8a-2e12d06a25b6"}
{"timestamp": "2025-05-28T06:41:17.848187+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.26}, "user_id": null, "request_id": "94e5e6ea-79f5-410c-b0ca-b53357c9efa4"}
{"timestamp": "2025-05-28T06:41:17.874485+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/auth.js - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/auth.js", "status_code": 200, "duration_ms": 0.89}, "user_id": null, "request_id": "4f67fea0-88d4-4a8e-ae8c-76a55692226f"}
{"timestamp": "2025-05-28T06:51:02.338595+00:00", "level": "INFO", "logger": "api", "message": "GET /favicon.ico - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/favicon.ico", "status_code": 404, "duration_ms": 0.55}, "user_id": null, "request_id": "9109842c-657b-408d-b8b5-8bfdb20b6dd5"}
{"timestamp": "2025-05-28T06:51:23.921171+00:00", "level": "INFO", "logger": "api", "message": "GET /favicon.ico - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/favicon.ico", "status_code": 404, "duration_ms": 0.95}, "user_id": null, "request_id": "c8864b28-aaa9-4fe0-9946-d49456a96338"}
{"timestamp": "2025-05-28T06:54:42.905840+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 5.1}, "user_id": null, "request_id": "449a77ef-4559-4714-aac2-b09c58a28869"}
{"timestamp": "2025-05-28T06:54:42.950188+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 12.55}, "user_id": null, "request_id": "356804b9-42d1-4c8c-ad32-d193c9a0b9dd"}
{"timestamp": "2025-05-28T06:54:42.950873+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 12.0}, "user_id": null, "request_id": "7169fcb0-fb15-47a1-9e8f-91d38c0629a7"}
{"timestamp": "2025-05-28T06:54:42.951283+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 11.99}, "user_id": null, "request_id": "dbc3ee92-ace7-4f46-9c0e-8e282ae05535"}
{"timestamp": "2025-05-28T06:54:42.957918+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.59}, "user_id": null, "request_id": "81eefc9a-b441-4375-819f-8fc950bf49fe"}
{"timestamp": "2025-05-28T06:54:44.389268+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.301s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 301.16}, "user_id": null, "request_id": "5334f3f2-37de-4a13-a968-f1e6511c1710"}
{"timestamp": "2025-05-28T06:54:45.407005+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.12}, "user_id": null, "request_id": "e16be0dc-226e-4c92-a89f-a283233191e1"}
{"timestamp": "2025-05-28T06:54:47.988701+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.557s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2557.04}, "user_id": null, "request_id": "89b8aab8-f95e-4b96-ba57-5b71a9d7c7c8"}
{"timestamp": "2025-05-28T06:54:47.989857+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.555s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2554.6}, "user_id": null, "request_id": "a6dec520-b180-4c9c-b91e-b41c76177104"}
{"timestamp": "2025-05-28T06:54:47.990434+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.555s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2554.79}, "user_id": null, "request_id": "15a1762f-1334-4f91-9952-3d0ff4bf8987"}
{"timestamp": "2025-05-28T06:55:01.977351+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 4.38}, "user_id": null, "request_id": "343e66a8-c8ee-40d5-988d-dd7ca65f6077"}
{"timestamp": "2025-05-28T06:55:04.988772+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.46}, "user_id": null, "request_id": "e59a08fe-609a-4370-85df-fcd0f1075603"}
{"timestamp": "2025-05-28T06:55:07.210812+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.204s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2203.93}, "user_id": null, "request_id": "6f370d0e-ae3f-4bf0-a771-8116f7b4346e"}
{"timestamp": "2025-05-28T06:55:07.211743+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.204s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2204.15}, "user_id": null, "request_id": "00fd4695-62ae-4710-9583-cb34753cddb2"}
{"timestamp": "2025-05-28T06:55:07.212328+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.204s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2204.07}, "user_id": null, "request_id": "7a042fcd-8be7-452f-9391-61fdfaa39c24"}
{"timestamp": "2025-05-28T06:55:42.959766+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 2.43}, "user_id": null, "request_id": "40cbc748-a147-4784-8a00-d41e46d95680"}
{"timestamp": "2025-05-28T06:55:44.867155+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.893s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1892.62}, "user_id": null, "request_id": "a53d899f-729a-47c7-ade8-21f63a339574"}
{"timestamp": "2025-05-28T07:02:27.997772+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 2.21}, "user_id": null, "request_id": "13e6d4ea-c246-49bd-8743-e41b33bff42f"}
{"timestamp": "2025-05-28T07:02:30.306046+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.287s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2286.91}, "user_id": null, "request_id": "359d4e4f-5a02-4578-868d-cc772f28e59b"}
{"timestamp": "2025-05-28T07:02:30.310403+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.291s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2290.82}, "user_id": null, "request_id": "3374b428-cdc5-4de5-a62c-3655fad31dc4"}
{"timestamp": "2025-05-28T07:02:30.310785+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.291s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2290.87}, "user_id": null, "request_id": "fcf20bb2-d866-4c6a-9c35-bd0919e02e91"}
{"timestamp": "2025-05-28T07:02:30.311459+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 5.91}, "user_id": null, "request_id": "58281a91-3a4c-4f13-b056-c8d2469bc75a"}
{"timestamp": "2025-05-28T07:02:32.465221+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 1.12}, "user_id": null, "request_id": "d21bf411-1bb8-4c82-ba0c-d13b5e523048"}
{"timestamp": "2025-05-28T07:02:44.012770+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.96}, "user_id": null, "request_id": "bc85857f-5e25-46ee-bb87-14f8d01372fe"}
{"timestamp": "2025-05-28T07:02:45.425218+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.235s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 235.07}, "user_id": null, "request_id": "6c8dcf1b-e598-4a7e-bca9-627164256087"}
{"timestamp": "2025-05-28T07:02:46.441215+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.51}, "user_id": null, "request_id": "df20153f-7280-4ed3-a9ff-cc34bd92f0e7"}
{"timestamp": "2025-05-28T07:02:48.622953+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.167s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2166.9}, "user_id": null, "request_id": "ac407585-04eb-4a8e-b11f-8bb9978dc197"}
{"timestamp": "2025-05-28T07:02:48.623475+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.167s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2167.37}, "user_id": null, "request_id": "9c33c8c2-7679-4179-b95b-06079d2860d5"}
{"timestamp": "2025-05-28T07:02:48.624247+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.168s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2168.02}, "user_id": null, "request_id": "00fabe5a-56de-48e9-a1a2-6ba7c868bd89"}
{"timestamp": "2025-05-28T07:02:53.511944+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.19}, "user_id": null, "request_id": "b08ba158-a6fa-404d-b6f9-0433f79276b2"}
{"timestamp": "2025-05-28T07:02:55.651079+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.124s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2123.63}, "user_id": null, "request_id": "e4991153-1533-4ed2-bf00-28954e706e94"}
{"timestamp": "2025-05-28T07:04:29.653100+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.67}, "user_id": null, "request_id": "a6626603-6393-4d80-9ef3-f85ed0f1cf2d"}
{"timestamp": "2025-05-28T07:04:31.768889+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.096s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2095.75}, "user_id": null, "request_id": "ed158cbe-c26d-49cd-84a6-c0e66476a1ba"}
{"timestamp": "2025-05-28T07:04:31.771659+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.097s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2097.32}, "user_id": null, "request_id": "8fdf320a-9baa-40ba-9c2e-486992e2cc7e"}
{"timestamp": "2025-05-28T07:04:31.772151+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.098s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2097.53}, "user_id": null, "request_id": "2a4ddddc-796a-4f05-8869-f1bca943791f"}
{"timestamp": "2025-05-28T07:04:31.773952+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.34}, "user_id": null, "request_id": "56886f3c-8315-47cd-a05f-436f477ef6d1"}
{"timestamp": "2025-05-28T07:04:34.269016+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.469s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2468.68}, "user_id": null, "request_id": "3380e1f7-e598-4d95-ac30-00f49eb07885"}
{"timestamp": "2025-05-28T07:05:37.804174+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/12/sync - 200 (53.929s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/12/sync", "status_code": 200, "duration_ms": 53929.19}, "user_id": null, "request_id": "5873a117-db43-4625-9f5a-c6b4409e5a02"}
{"timestamp": "2025-05-28T07:05:39.828600+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.019s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2018.66}, "user_id": null, "request_id": "4d3d15be-c40a-4e70-94c0-4295e902c3ef"}
{"timestamp": "2025-05-28T07:12:58.940180+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 3.67}, "user_id": null, "request_id": "8262d4bd-f83d-428e-b1f7-63137aeb6f3c"}
{"timestamp": "2025-05-28T07:13:00.377919+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.99}, "user_id": null, "request_id": "499ea666-97c9-4475-b4c0-e743455ce9d0"}
{"timestamp": "2025-05-28T07:13:02.454338+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.057s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2057.28}, "user_id": null, "request_id": "9c56b252-3fea-4929-8bc2-6fbb1e57bc66"}
{"timestamp": "2025-05-28T07:13:02.455033+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.058s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2057.92}, "user_id": null, "request_id": "89b5285f-f25c-4259-b18f-a5615da24bb6"}
{"timestamp": "2025-05-28T07:13:02.455392+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.058s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2058.27}, "user_id": null, "request_id": "c324f6a4-26bb-4b44-b068-8d6869b315ef"}
{"timestamp": "2025-05-28T07:13:05.417276+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 6.81}, "user_id": null, "request_id": "d5d03d48-1855-41f0-b11c-e16acea757e6"}
{"timestamp": "2025-05-28T07:13:07.026559+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 0.47}, "user_id": null, "request_id": "44fa931c-9d37-4135-8c76-2290be0112c2"}
{"timestamp": "2025-05-28T07:13:35.960459+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.46}, "user_id": null, "request_id": "be08b5c7-2888-4fdc-bb82-1da26f198b92"}
{"timestamp": "2025-05-28T07:13:37.429628+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.240s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 239.78}, "user_id": null, "request_id": "a40547bd-f0ec-4418-a490-b6ed7680bf2c"}
{"timestamp": "2025-05-28T07:13:38.445510+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.22}, "user_id": null, "request_id": "537296ff-961d-4c0d-8017-ea5fc112cb6a"}
{"timestamp": "2025-05-28T07:13:40.640341+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.174s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2173.68}, "user_id": null, "request_id": "6b46b573-1d15-4fa6-9f91-a5fbcb2e565d"}
{"timestamp": "2025-05-28T07:13:40.643000+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.175s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2175.43}, "user_id": null, "request_id": "c828f51f-7a44-4767-8a3e-d9c3edfedbae"}
{"timestamp": "2025-05-28T07:13:40.643592+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.175s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2175.34}, "user_id": null, "request_id": "431b3d39-7d22-45ab-a7a3-34653cb55f1a"}
{"timestamp": "2025-05-28T07:13:49.989842+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 4.83}, "user_id": null, "request_id": "34f7306f-a0dd-4ef6-aebf-1aac03fccfbf"}
{"timestamp": "2025-05-28T07:13:50.003350+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 4.95}, "user_id": null, "request_id": "d5ec4c09-9d0d-4536-9a21-b1ee8876ed45"}
{"timestamp": "2025-05-28T07:14:04.833511+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 7.1}, "user_id": null, "request_id": "1b8c2b46-f078-41c7-91bb-813ab1900037"}
{"timestamp": "2025-05-28T07:14:06.642808+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.791s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1791.07}, "user_id": null, "request_id": "6db752ef-57fc-43f7-8e88-f0ce76890b68"}
{"timestamp": "2025-05-28T07:14:56.569328+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.64}, "user_id": null, "request_id": "52719b6a-df1b-47f9-aaad-c295f7270e61"}
{"timestamp": "2025-05-28T07:14:58.502928+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.918s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1918.09}, "user_id": null, "request_id": "28ea305c-04a7-4ce0-afb2-e652fb34a29b"}
{"timestamp": "2025-05-28T07:14:58.504058+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (1.919s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 1918.5}, "user_id": null, "request_id": "7255d9ee-e74f-4da8-a4f4-6cc77291f97e"}
{"timestamp": "2025-05-28T07:14:58.504568+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (1.918s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 1918.18}, "user_id": null, "request_id": "c9ca5290-e78e-42ac-9ed4-88b7e9506416"}
{"timestamp": "2025-05-28T07:15:17.226545+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.37}, "user_id": null, "request_id": "f151d72f-2fad-40b9-b84c-567c2f1f1c8a"}
{"timestamp": "2025-05-28T07:15:19.278845+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2029.5}, "user_id": null, "request_id": "f655fab9-d3b5-4ce3-8781-0cbc033a6b75"}
{"timestamp": "2025-05-28T07:15:21.725982+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 1.06}, "user_id": null, "request_id": "7811a6ed-d364-45fa-b25d-b7d7167e8001"}
{"timestamp": "2025-05-28T07:15:21.740393+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 6.48}, "user_id": null, "request_id": "154674b9-e069-4232-a856-ed36fb40b978"}
{"timestamp": "2025-05-28T07:16:20.528829+00:00", "level": "INFO", "logger": "api", "message": "GET /profile - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/profile", "status_code": 200, "duration_ms": 4.95}, "user_id": null, "request_id": "f4a831a0-1b91-485d-aa46-4f879c3143ec"}
{"timestamp": "2025-05-28T07:16:20.541440+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/style.css - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/style.css", "status_code": 404, "duration_ms": 1.35}, "user_id": null, "request_id": "ad49cad1-eabb-4497-94cf-9ef8e4ad159a"}
{"timestamp": "2025-05-28T07:16:20.555738+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.29}, "user_id": null, "request_id": "a8a2fe4f-3545-4aaf-b50e-82d964be0814"}
{"timestamp": "2025-05-28T07:16:20.565892+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 6.97}, "user_id": null, "request_id": "d59d95e8-b47b-4208-b873-502d7ed6f73c"}
{"timestamp": "2025-05-28T07:16:25.343731+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.39}, "user_id": null, "request_id": "404748f3-eaac-4225-8c5f-bb400213be6b"}
{"timestamp": "2025-05-28T07:16:27.518683+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.153s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2153.04}, "user_id": null, "request_id": "701cf5bd-29d0-4eaa-9ae3-eb0e51f0e799"}
{"timestamp": "2025-05-28T07:16:27.529138+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.162s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2161.8}, "user_id": null, "request_id": "2b9f976e-3764-4a67-9ae9-475078429819"}
{"timestamp": "2025-05-28T07:16:27.530566+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 13.4}, "user_id": null, "request_id": "2cd524f9-f407-46a7-9cf0-c6c12afedb50"}
{"timestamp": "2025-05-28T07:16:28.258766+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.31}, "user_id": null, "request_id": "782325c7-958f-4888-9fe7-da04af5e51b6"}
{"timestamp": "2025-05-28T07:16:29.741690+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.220s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 220.07}, "user_id": null, "request_id": "597c657b-aa9c-4638-a03f-87029d0f31bd"}
{"timestamp": "2025-05-28T07:16:30.758681+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.13}, "user_id": null, "request_id": "47e33eb5-cc97-4b3e-b58c-a656a0f5f625"}
{"timestamp": "2025-05-28T07:16:32.850083+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.071s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2070.71}, "user_id": null, "request_id": "b5e720d5-9ac8-490a-b0be-842551a1cd23"}
{"timestamp": "2025-05-28T07:16:32.851099+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.071s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2071.21}, "user_id": null, "request_id": "9368ac3f-21e1-4b54-9285-d3e404989e2c"}
{"timestamp": "2025-05-28T07:16:32.851453+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.071s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2071.4}, "user_id": null, "request_id": "a6e1320f-5547-4580-a99d-322901840e95"}
{"timestamp": "2025-05-28T07:17:45.258885+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 1.14}, "user_id": null, "request_id": "59e610d3-1ad9-42d5-a294-ad8c40b3c15d"}
{"timestamp": "2025-05-28T07:17:45.276693+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 6.37}, "user_id": null, "request_id": "8548297c-aec7-416b-98fa-0ac1efd6cded"}
{"timestamp": "2025-05-28T07:23:16.487241+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 1.12}, "user_id": null, "request_id": "59500350-568a-48d4-aa28-de7112d175c3"}
{"timestamp": "2025-05-28T07:23:16.504465+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 8.59}, "user_id": null, "request_id": "710a1399-d7f3-462b-86fa-08c0b9e5b8fa"}
{"timestamp": "2025-05-28T07:23:41.664615+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.16}, "user_id": null, "request_id": "b1d38c82-0349-4888-b964-601164f4b256"}
{"timestamp": "2025-05-28T07:23:43.838378+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.154s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2154.14}, "user_id": null, "request_id": "d6f6c212-9466-455d-bb1b-ea4018ab73b3"}
{"timestamp": "2025-05-28T07:30:51.651540+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.25}, "user_id": null, "request_id": "77dcd4db-df16-4aa5-9afa-3cac36c06908"}
{"timestamp": "2025-05-28T07:30:53.854353+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.183s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2183.21}, "user_id": null, "request_id": "e267c8a5-eaea-4c80-874d-788fde80528c"}
{"timestamp": "2025-05-28T07:30:53.855927+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.183s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2183.24}, "user_id": null, "request_id": "1e78058d-7df1-4917-a6fe-dbccb4113bcd"}
{"timestamp": "2025-05-28T07:30:53.856618+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.183s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2183.41}, "user_id": null, "request_id": "53ef5369-f78b-404d-bf92-58f8da90f093"}
{"timestamp": "2025-05-28T07:30:53.858164+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 4.0}, "user_id": null, "request_id": "af818a00-7ba3-40ec-a5c7-9bd93484add9"}
{"timestamp": "2025-05-28T07:30:53.888374+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 6.73}, "user_id": null, "request_id": "75dfdd7a-54fc-4dc3-aba9-ed5e6536e56e"}
{"timestamp": "2025-05-28T07:33:20.912280+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 0.82}, "user_id": null, "request_id": "75b0a5c7-b790-4192-a325-28de5e05896b"}
{"timestamp": "2025-05-28T07:33:20.927591+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 8.01}, "user_id": null, "request_id": "841bb22a-ab5b-420e-8071-8dd3a3a468b7"}
{"timestamp": "2025-05-28T07:33:39.444540+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 0.83}, "user_id": null, "request_id": "5a229f84-fa12-4d14-b8f3-5a42db44a840"}
{"timestamp": "2025-05-28T07:33:39.458513+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 7.06}, "user_id": null, "request_id": "1a938a63-b757-46e6-a1e3-d44c2b6811d8"}
{"timestamp": "2025-05-28T07:35:50.126447+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 0.62}, "user_id": null, "request_id": "4ad945da-1dc7-4f3f-8557-737f0adfdc2d"}
{"timestamp": "2025-05-28T07:35:50.142299+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 6.76}, "user_id": null, "request_id": "485045b8-15ae-4f85-a1ce-48b6adcb775d"}
{"timestamp": "2025-05-28T07:36:08.985251+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (5.235s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 5234.9}, "user_id": null, "request_id": "1dac8420-2f36-445d-8693-a266d4b1f941"}
{"timestamp": "2025-05-28T07:36:32.486159+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (4.736s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 4736.15}, "user_id": null, "request_id": "46584a0e-a3bf-497f-a304-f9eca15620ac"}
{"timestamp": "2025-05-28T07:36:59.722787+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (5.709s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 5709.24}, "user_id": null, "request_id": "3abdffe8-453e-423f-bc96-ca66eec3d0e8"}
{"timestamp": "2025-05-28T07:37:26.042860+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (5.772s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 5772.05}, "user_id": null, "request_id": "505da119-d6a8-4f07-ad6d-97a8391f3f50"}
{"timestamp": "2025-05-28T07:37:44.885101+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (5.067s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 5066.96}, "user_id": null, "request_id": "a81fb411-5bbc-453d-89da-79b66a615874"}
{"timestamp": "2025-05-28T07:38:13.973764+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (5.747s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 5746.95}, "user_id": null, "request_id": "20c4c754-fc88-485c-b673-1156db283a24"}
{"timestamp": "2025-05-28T07:38:52.572206+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (14.511s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 14511.14}, "user_id": null, "request_id": "9bd41a19-480a-498b-a340-f15400e1d776"}
{"timestamp": "2025-05-28T07:53:41.770647+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.0}, "user_id": null, "request_id": "99bdfea2-34ed-4e3d-81ee-c50ddce243fd"}
{"timestamp": "2025-05-28T07:53:41.787723+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 3.42}, "user_id": null, "request_id": "85abe2a2-1810-47cc-b46d-520143cf8715"}
{"timestamp": "2025-05-28T07:53:41.788288+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 3.41}, "user_id": null, "request_id": "d305fab1-4b07-413e-84aa-352de765052f"}
{"timestamp": "2025-05-28T07:53:41.788453+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 2.94}, "user_id": null, "request_id": "bca86495-f61f-4522-a8a4-304489190181"}
{"timestamp": "2025-05-28T07:53:41.792544+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.53}, "user_id": null, "request_id": "1bf03374-0c84-4146-92b4-4d98e874f5cd"}
{"timestamp": "2025-05-28T07:53:43.729325+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 1.79}, "user_id": null, "request_id": "f6a4e665-4587-4155-bbca-c875fd51cf79"}
{"timestamp": "2025-05-28T07:53:44.703516+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.57}, "user_id": null, "request_id": "a439f24b-83a6-4d04-ba4e-8f197272d665"}
{"timestamp": "2025-05-28T07:53:45.916325+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.225s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 225.19}, "user_id": null, "request_id": "968a647f-ad13-4829-ac6e-d7853f82a910"}
{"timestamp": "2025-05-28T07:53:46.931960+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.12}, "user_id": null, "request_id": "66bfe285-b59a-47c1-9f7f-acb9a2ef70ee"}
{"timestamp": "2025-05-28T07:53:49.439692+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.482s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2482.46}, "user_id": null, "request_id": "417932d9-51b4-4e50-892a-8d45dd99780e"}
{"timestamp": "2025-05-28T07:53:49.440199+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.483s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2482.91}, "user_id": null, "request_id": "84851541-e37b-49cf-99fe-8b1f08388f19"}
{"timestamp": "2025-05-28T07:53:49.440474+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.483s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2482.77}, "user_id": null, "request_id": "5f550144-7bf7-458e-928c-5bba3f35ee04"}
{"timestamp": "2025-05-28T08:03:11.251966+00:00", "level": "INFO", "logger": "api", "message": "GET /profile - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/profile", "status_code": 200, "duration_ms": 6.12}, "user_id": null, "request_id": "5df88b7b-93cc-4eb5-8b0c-757872dba291"}
{"timestamp": "2025-05-28T08:03:11.268500+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/style.css - 404 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/style.css", "status_code": 404, "duration_ms": 1.59}, "user_id": null, "request_id": "6e00c105-4db3-4425-9881-8a1f4f1b2150"}
{"timestamp": "2025-05-28T08:03:11.293036+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.020s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 20.22}, "user_id": null, "request_id": "05089efb-d098-44cc-8d3a-ffcca5874712"}
{"timestamp": "2025-05-28T08:03:11.299050+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 4.55}, "user_id": null, "request_id": "a948d5c7-6a64-4511-b6a8-d1a6d4f21a39"}
{"timestamp": "2025-05-28T08:03:12.961268+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 7.05}, "user_id": null, "request_id": "14e61ebf-0c4a-4272-a7f5-7e6ebeae86aa"}
{"timestamp": "2025-05-28T08:03:15.780832+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.799s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2798.85}, "user_id": null, "request_id": "9397fdc4-dab4-4aa2-a3f9-d7ad6efb8c88"}
{"timestamp": "2025-05-28T08:03:15.791124+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.808s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2808.25}, "user_id": null, "request_id": "93ed71f4-6914-430e-8015-ca764f8becad"}
{"timestamp": "2025-05-28T08:03:15.793763+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.809s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2808.91}, "user_id": null, "request_id": "f71f30df-2a46-4b3e-87f3-52e6e313adfe"}
{"timestamp": "2025-05-28T08:03:15.794849+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 14.32}, "user_id": null, "request_id": "dadfd1e7-407e-4317-822b-76553cdba844"}
{"timestamp": "2025-05-28T08:03:18.193076+00:00", "level": "INFO", "logger": "api", "message": "GET /register - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/register", "status_code": 200, "duration_ms": 4.09}, "user_id": null, "request_id": "7343dbc5-fa23-4067-a77a-c82c6d1e106c"}
{"timestamp": "2025-05-28T08:03:20.789114+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 3.9}, "user_id": null, "request_id": "8e3e4efa-967c-461d-b51f-b642f8c408e4"}
{"timestamp": "2025-05-28T08:03:24.689279+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.6}, "user_id": null, "request_id": "4945618c-54f0-4baf-b6ad-ae64885269fc"}
{"timestamp": "2025-05-28T08:03:26.985367+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.236s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 236.23}, "user_id": null, "request_id": "03f7bd39-157c-4863-80ac-e7fc89127f93"}
{"timestamp": "2025-05-28T08:03:28.001806+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.21}, "user_id": null, "request_id": "4cea70b8-d91c-4aa4-b801-21ac68de3244"}
{"timestamp": "2025-05-28T08:03:29.967556+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.942s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1942.28}, "user_id": null, "request_id": "77156f50-988a-41c8-a957-16cb8b4dced8"}
{"timestamp": "2025-05-28T08:03:29.970948+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (1.944s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 1944.48}, "user_id": null, "request_id": "be467b37-6f0e-440c-8e0f-19821b90df5c"}
{"timestamp": "2025-05-28T08:03:29.972775+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (1.945s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 1944.64}, "user_id": null, "request_id": "422e85c0-680d-4c04-934b-2c9f794d764f"}
{"timestamp": "2025-05-28T08:03:54.062149+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 7.53}, "user_id": null, "request_id": "16524fa5-44c2-4103-acb2-1cead0f2d40e"}
{"timestamp": "2025-05-28T08:03:56.075575+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.998s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1997.86}, "user_id": null, "request_id": "73fbccc0-9340-4cf2-aab8-8eefc780c8cf"}
{"timestamp": "2025-05-28T08:04:22.493737+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/12/sync - 200 (23.541s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/12/sync", "status_code": 200, "duration_ms": 23540.66}, "user_id": null, "request_id": "5641fed0-c87a-45f2-95bc-0d9014a2b1d6"}
{"timestamp": "2025-05-28T08:04:24.417950+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.921s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1920.96}, "user_id": null, "request_id": "9e03bf96-34c3-4502-b77e-2abf362c4627"}
{"timestamp": "2025-05-28T09:32:13.509676+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.8}, "user_id": null, "request_id": "f12fc3f1-6da1-45da-869a-7e6cd955ab19"}
{"timestamp": "2025-05-28T09:32:13.538802+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 8.7}, "user_id": null, "request_id": "ed480c83-ef91-4703-ae2b-3578918efb5e"}
{"timestamp": "2025-05-28T09:32:13.540022+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.010s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 9.87}, "user_id": null, "request_id": "53870f9d-0811-4995-a8da-67313e967723"}
{"timestamp": "2025-05-28T09:32:13.540282+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.010s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 10.38}, "user_id": null, "request_id": "4b65c8c5-575f-4038-bde9-7b35633add43"}
{"timestamp": "2025-05-28T09:32:13.544781+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.56}, "user_id": null, "request_id": "2250da41-f1ba-496c-9878-0a7716250984"}
{"timestamp": "2025-05-28T09:32:14.695290+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.228s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 228.0}, "user_id": null, "request_id": "41d0f135-bfad-472b-afbf-7e6a8096a681"}
{"timestamp": "2025-05-28T09:32:15.716286+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.13}, "user_id": null, "request_id": "81776810-0104-4be8-b99a-70d044727490"}
{"timestamp": "2025-05-28T09:32:17.766046+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2029.29}, "user_id": null, "request_id": "a678cb75-db73-46ca-85e7-3191d6cab0cf"}
{"timestamp": "2025-05-28T09:32:17.766984+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.030s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2029.72}, "user_id": null, "request_id": "702be31f-c35f-4360-b459-2b8a869e89ff"}
{"timestamp": "2025-05-28T09:32:17.767327+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.030s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2029.96}, "user_id": null, "request_id": "53410a38-8068-419f-994d-3d1314a7e7af"}
{"timestamp": "2025-05-28T09:32:20.086655+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.53}, "user_id": null, "request_id": "3535399b-5ce7-434f-89c9-1bb3e7f9fb3d"}
{"timestamp": "2025-05-28T09:32:20.104384+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 2.28}, "user_id": null, "request_id": "94676b74-f667-45eb-b953-df048528bedd"}
{"timestamp": "2025-05-28T09:32:22.017094+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.909s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1909.28}, "user_id": null, "request_id": "24864102-ad20-4f7f-9e23-3f77e586609d"}
{"timestamp": "2025-05-28T09:32:24.742127+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/12 - 200 (0.034s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/12", "status_code": 200, "duration_ms": 33.93}, "user_id": null, "request_id": "af6fd778-8cca-4100-b9ab-b5b228f0987d"}
{"timestamp": "2025-05-28T09:32:24.754342+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7.09}, "user_id": null, "request_id": "a0fa7d9d-4ae5-4b96-ac08-a36d5a14a883"}
{"timestamp": "2025-05-28T09:32:28.259000+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 2.73}, "user_id": null, "request_id": "9c18c8bf-56ba-4de2-a7cb-e19c7f8a7285"}
{"timestamp": "2025-05-28T09:32:28.294034+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 16.61}, "user_id": null, "request_id": "71fe0f31-7f5e-4a5e-b6f3-1d033bdf8eac"}
{"timestamp": "2025-05-28T09:32:28.294872+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 16.82}, "user_id": null, "request_id": "08344b6b-606d-4ec0-a8a8-77d7258d7c74"}
{"timestamp": "2025-05-28T09:32:28.295222+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 16.65}, "user_id": null, "request_id": "407d02ac-2983-4131-bf1e-c333104ff425"}
{"timestamp": "2025-05-28T09:32:29.081136+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.55}, "user_id": null, "request_id": "151af1f6-3e7e-499a-b44a-6f9db2047ceb"}
{"timestamp": "2025-05-28T09:32:31.417791+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.201s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 201.39}, "user_id": null, "request_id": "619831ac-231c-4df6-b09d-5214a7224656"}
{"timestamp": "2025-05-28T09:32:32.433305+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.55}, "user_id": null, "request_id": "99c1eff3-4b17-4f8b-9fa6-4116bc61a303"}
{"timestamp": "2025-05-28T09:32:34.429802+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.987s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1987.15}, "user_id": null, "request_id": "640df2c8-9e29-4539-af1b-450899152d44"}
{"timestamp": "2025-05-28T09:32:34.431303+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (1.987s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 1986.5}, "user_id": null, "request_id": "3bcabc6a-6fb2-455d-87d1-fda31d76b9e9"}
{"timestamp": "2025-05-28T09:32:34.431842+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (1.987s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 1986.93}, "user_id": null, "request_id": "d9f06649-2eb3-4d5a-9610-64b4c5827f9a"}
{"timestamp": "2025-05-28T09:32:34.433128+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 3.68}, "user_id": null, "request_id": "31df69c0-f662-428d-a339-b0b3cfdca7b6"}
{"timestamp": "2025-05-28T09:32:36.622883+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.170s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2169.76}, "user_id": null, "request_id": "b5804bd1-e02a-4c8d-9fa2-7e8d9b4e3f41"}
{"timestamp": "2025-05-28T09:32:40.191731+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/7 - 200 (0.023s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/7", "status_code": 200, "duration_ms": 23.37}, "user_id": null, "request_id": "93496c84-e4b2-4dd0-87a8-bb0b594e7ec7"}
{"timestamp": "2025-05-28T09:32:40.201034+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6.21}, "user_id": null, "request_id": "324252dc-48b7-4c1b-ade8-191ef3a53458"}
{"timestamp": "2025-05-28T09:32:42.501963+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.2}, "user_id": null, "request_id": "c86b21ec-74a5-4967-8a8f-fcb9b34b0539"}
{"timestamp": "2025-05-28T09:32:42.537026+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 14.3}, "user_id": null, "request_id": "7f977914-541d-45bc-95e9-b8295a93c8a7"}
{"timestamp": "2025-05-28T09:32:42.538012+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 14.7}, "user_id": null, "request_id": "7225d8e1-def7-400d-abbf-857004e9d9ac"}
{"timestamp": "2025-05-28T09:32:42.538282+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 14.54}, "user_id": null, "request_id": "1a21d319-6217-475f-8dbf-f932c4e82724"}
{"timestamp": "2025-05-28T09:37:58.958844+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.04}, "user_id": null, "request_id": "e1aec70c-c29d-4048-9787-68650f504c8e"}
{"timestamp": "2025-05-28T09:38:00.491435+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 401 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 401, "duration_ms": 28.77}, "user_id": null, "request_id": "6e21bae9-4f4e-45b1-b1c6-bd24583a792b"}
{"timestamp": "2025-05-28T09:38:04.091003+00:00", "level": "INFO", "logger": "api", "message": "GET /register - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/register", "status_code": 200, "duration_ms": 4.84}, "user_id": null, "request_id": "8f2c5bfc-54e6-4ce2-b1d9-4bf94d01f974"}
{"timestamp": "2025-05-28T09:38:07.926473+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 7.41}, "user_id": null, "request_id": "9730562b-f223-4cac-af7b-d01cf5f21b54"}
{"timestamp": "2025-05-28T09:38:09.016828+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.11}, "user_id": null, "request_id": "13874867-4223-4806-af13-4408fc142b11"}
{"timestamp": "2025-05-28T09:38:11.215693+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 401 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 401, "duration_ms": 4.69}, "user_id": null, "request_id": "36b12a3e-9eeb-4aa8-9e7a-3653c0b4492f"}
{"timestamp": "2025-05-28T09:38:12.812880+00:00", "level": "INFO", "logger": "api", "message": "GET /register - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/register", "status_code": 200, "duration_ms": 0.56}, "user_id": null, "request_id": "726d0c86-14dd-44c6-bbc9-8da732b8282c"}
{"timestamp": "2025-05-28T09:38:21.486823+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/register - 201 (0.257s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/register", "status_code": 201, "duration_ms": 256.55}, "user_id": null, "request_id": "955f54da-05a6-4af2-b509-1bb05b07e49f"}
{"timestamp": "2025-05-28T09:38:22.501246+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 5.57}, "user_id": null, "request_id": "2aac5ec9-9efb-459a-b3d5-01b967fc14b4"}
{"timestamp": "2025-05-28T09:38:22.542892+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.024s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 24.22}, "user_id": null, "request_id": "bfe9d4ff-f54c-470d-b103-208b3ef579bb"}
{"timestamp": "2025-05-28T09:38:22.543496+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.023s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 22.67}, "user_id": null, "request_id": "8c16fd5c-707d-4741-b8d1-a34e5e45a65b"}
{"timestamp": "2025-05-28T09:38:22.543836+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.023s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 22.56}, "user_id": null, "request_id": "51668a70-6c0b-4afa-9355-4efe18945bf3"}
{"timestamp": "2025-05-28T09:38:25.921072+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 8.11}, "user_id": null, "request_id": "392fd5bb-db31-49ab-b330-168b3d16fec0"}
{"timestamp": "2025-05-28T09:38:25.940472+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.78}, "user_id": null, "request_id": "c4e85a8c-7385-4786-8b2a-077f70f16894"}
{"timestamp": "2025-05-28T09:38:30.562457+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (0.448s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 448.41}, "user_id": null, "request_id": "83fd4334-7fee-4e74-8147-fcfd7567421a"}
{"timestamp": "2025-05-28T09:39:08.725985+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (37.164s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 37163.92}, "user_id": null, "request_id": "2f64405d-d6c8-432f-b896-c0883ecd8d1b"}
{"timestamp": "2025-05-28T09:39:10.864455+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.135s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2135.05}, "user_id": null, "request_id": "300a26ed-f857-4913-82a5-70ee3fd9a859"}
{"timestamp": "2025-05-28T10:01:15.677455+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/1/sync - 200 (83.747s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/1/sync", "status_code": 200, "duration_ms": 83746.6}, "user_id": null, "request_id": "155ea409-27d8-4819-a514-898857f7ca0a"}
{"timestamp": "2025-05-28T10:01:18.720987+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.037s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3037.1}, "user_id": null, "request_id": "a289d144-da76-4e22-85d6-bb9122cb97a6"}
{"timestamp": "2025-05-28T10:04:08.187520+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 16.82}, "user_id": null, "request_id": "5ecd83ea-c85e-495f-ba78-f187077b7a19"}
{"timestamp": "2025-05-28T10:04:13.509871+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (5.301s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5301.19}, "user_id": null, "request_id": "d88a58b6-b57f-4c39-9610-8b3bc5be1d3e"}
{"timestamp": "2025-05-28T10:04:13.514106+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (5.304s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 5304.45}, "user_id": null, "request_id": "2590fc0f-1415-47db-b927-09d1e51f20a5"}
{"timestamp": "2025-05-28T10:04:13.514814+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (5.305s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 5305.12}, "user_id": null, "request_id": "ed2c0785-e3d5-471d-87bd-622c63dc301b"}
{"timestamp": "2025-05-28T10:04:13.515820+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 6.3}, "user_id": null, "request_id": "71598a37-2a26-4bfd-8575-fb6642f6fbf4"}
{"timestamp": "2025-05-28T10:04:17.583506+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.246s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 246.45}, "user_id": null, "request_id": "89d2fef4-3e7f-452c-9471-310d07ec1779"}
{"timestamp": "2025-05-28T10:04:18.598661+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.08}, "user_id": null, "request_id": "05a99bb1-24d4-41cd-8df0-8c7ed770591f"}
{"timestamp": "2025-05-28T10:04:20.832407+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.223s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2223.27}, "user_id": null, "request_id": "1689b9a7-91f0-474e-8526-377f64e7b1c3"}
{"timestamp": "2025-05-28T10:04:20.844026+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.231s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2231.45}, "user_id": null, "request_id": "19cbd61f-6b6c-4f1c-9d53-5cd3319ad2ad"}
{"timestamp": "2025-05-28T10:04:20.844659+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 13.82}, "user_id": null, "request_id": "c001d62a-9540-4ec6-986c-5568a7a981b3"}
{"timestamp": "2025-05-28T10:04:22.698628+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 3.87}, "user_id": null, "request_id": "41892a0e-4603-442f-9e52-fad5bc4d5ac9"}
{"timestamp": "2025-05-28T10:04:36.111657+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (13.399s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 13398.85}, "user_id": null, "request_id": "b4b52d46-ba56-4b69-ae44-ccf9e344a343"}
{"timestamp": "2025-05-28T10:04:36.113913+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 3.32}, "user_id": null, "request_id": "abdc78ac-4147-4c2f-b6ee-865794aaec0d"}
{"timestamp": "2025-05-28T10:04:38.924885+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.790s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2790.0}, "user_id": null, "request_id": "caa87ac4-da50-4ab7-9ab3-f9bd8f48cc07"}
{"timestamp": "2025-05-28T10:04:38.926052+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.791s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2790.84}, "user_id": null, "request_id": "f2999cde-630b-4e7c-8bb1-c22d92853320"}
{"timestamp": "2025-05-28T10:04:38.926521+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.791s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2791.02}, "user_id": null, "request_id": "5bf09020-e530-46bb-a6f9-a9846d70a0c4"}
{"timestamp": "2025-05-28T10:04:38.927433+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.77}, "user_id": null, "request_id": "6a1fd5df-6200-4973-8efe-0dc8aafa40cf"}
{"timestamp": "2025-05-28T10:04:41.260280+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 401 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 401, "duration_ms": 6.58}, "user_id": null, "request_id": "a6d7cb57-b1b0-492e-8cc2-6f918d615545"}
{"timestamp": "2025-05-28T10:04:42.307005+00:00", "level": "INFO", "logger": "api", "message": "GET /register - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/register", "status_code": 200, "duration_ms": 3.07}, "user_id": null, "request_id": "2af0402a-4839-45ca-8ea7-1f4d6f09ef6e"}
{"timestamp": "2025-05-28T10:04:52.830102+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/register - 201 (0.238s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/register", "status_code": 201, "duration_ms": 238.15}, "user_id": null, "request_id": "2852ede2-6a4a-4fbc-978b-05cd8666f72a"}
{"timestamp": "2025-05-28T10:04:53.861599+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.8}, "user_id": null, "request_id": "ed2db463-c57e-474e-a6bb-ca4444ab1287"}
{"timestamp": "2025-05-28T10:04:53.888034+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.86}, "user_id": null, "request_id": "7a4d783d-4b78-4878-8649-22c7b15915dc"}
{"timestamp": "2025-05-28T10:04:53.888317+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 8.84}, "user_id": null, "request_id": "3088237a-839b-4e4d-8d8d-3bb9f8193323"}
{"timestamp": "2025-05-28T10:04:53.888472+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 7.67}, "user_id": null, "request_id": "89cbb0a6-33a9-4cd7-a905-ec7b6ae9bdaa"}
{"timestamp": "2025-05-28T10:04:55.885138+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.66}, "user_id": null, "request_id": "88cb567f-6c1a-4e7e-9db1-d5b78feea5bc"}
{"timestamp": "2025-05-28T10:04:55.903114+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.57}, "user_id": null, "request_id": "f27953c5-64c3-475d-8bbf-c0b1980e78fa"}
{"timestamp": "2025-05-28T10:04:59.632501+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (0.462s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 461.54}, "user_id": null, "request_id": "5fdb95c3-da4e-4c5f-bde2-c68b3b78d23d"}
{"timestamp": "2025-05-28T10:05:00.724869+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (0.504s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 503.61}, "user_id": null, "request_id": "47aac75f-7e9f-4167-aa10-37f310903ec5"}
{"timestamp": "2025-05-28T10:05:02.952179+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.213s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2212.54}, "user_id": null, "request_id": "a1450bbe-7945-4e3e-b2bb-46ca619b9719"}
{"timestamp": "2025-05-28T10:06:04.136299+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/2/sync - 200 (33.804s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/2/sync", "status_code": 200, "duration_ms": 33804.03}, "user_id": null, "request_id": "54ad8c10-8f97-4c2a-a714-9c3834a520b5"}
{"timestamp": "2025-05-28T10:06:07.506607+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.367s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3366.64}, "user_id": null, "request_id": "3585ea89-5688-483c-a52f-d21d800a3a17"}
{"timestamp": "2025-05-28T10:07:11.600577+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/2 - 200 (0.045s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/2", "status_code": 200, "duration_ms": 45.27}, "user_id": null, "request_id": "5eec2ebd-590b-4f80-8d73-e04635404844"}
{"timestamp": "2025-05-28T10:07:11.612980+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6.1}, "user_id": null, "request_id": "26d64f38-33ad-450b-a10e-38f7f1375d78"}
{"timestamp": "2025-05-28T10:07:13.512625+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 12.35}, "user_id": null, "request_id": "bb78ad4f-5436-45c7-a747-739f83c4f0bb"}
{"timestamp": "2025-05-28T10:07:13.554748+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 29.12}, "user_id": null, "request_id": "562a5600-05d8-4721-ab7c-657a84f946ba"}
{"timestamp": "2025-05-28T10:07:13.556413+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.030s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 29.64}, "user_id": null, "request_id": "e8cd51c0-033f-4c84-9570-c813c520aaad"}
{"timestamp": "2025-05-28T10:07:13.557047+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 29.35}, "user_id": null, "request_id": "7d5aadfd-6e36-4a01-b8d9-ce3eda5d264e"}
{"timestamp": "2025-05-28T10:07:15.120556+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 5.38}, "user_id": null, "request_id": "bf55609f-4514-40d4-9fe8-2391721560c2"}
{"timestamp": "2025-05-28T10:07:17.755409+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.235s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 235.49}, "user_id": null, "request_id": "509a65c9-7e70-457e-81d5-91dfaca832d8"}
{"timestamp": "2025-05-28T10:07:18.766489+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.62}, "user_id": null, "request_id": "660efeeb-7749-403c-9485-b9fb7b13e811"}
{"timestamp": "2025-05-28T10:07:22.066360+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.286s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3286.32}, "user_id": null, "request_id": "67a28577-fdec-47bc-a775-d076723c162c"}
{"timestamp": "2025-05-28T10:07:22.069882+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (3.289s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 3289.03}, "user_id": null, "request_id": "9125aa90-7fe9-4165-8e19-d2aaacc1547d"}
{"timestamp": "2025-05-28T10:07:22.072149+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (3.290s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 3290.1}, "user_id": null, "request_id": "577d03ed-e789-4f8c-b95b-73ebdb36639b"}
{"timestamp": "2025-05-28T10:07:25.171891+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 4.87}, "user_id": null, "request_id": "9085aad8-e6e4-4209-81cd-f58db594760c"}
{"timestamp": "2025-05-28T10:07:29.116474+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.928s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3928.16}, "user_id": null, "request_id": "e13ccb8d-c76a-4388-9a7e-0ea91b81dbc0"}
{"timestamp": "2025-05-28T10:07:32.672232+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/1 - 200 (0.840s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/1", "status_code": 200, "duration_ms": 839.69}, "user_id": null, "request_id": "7ff596e8-625b-4acb-8b18-22576050e138"}
{"timestamp": "2025-05-28T10:07:32.737196+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.9}, "user_id": null, "request_id": "4215f528-a00c-48dc-9400-1a57fcb973f8"}
{"timestamp": "2025-05-28T10:07:34.083545+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.58}, "user_id": null, "request_id": "09c70c62-5909-4a5c-81d1-ebc1300e69fc"}
{"timestamp": "2025-05-28T10:07:34.110621+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 13.33}, "user_id": null, "request_id": "517a8fa9-f160-4794-9f07-9ffd6e52fcef"}
{"timestamp": "2025-05-28T10:07:34.111238+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 12.93}, "user_id": null, "request_id": "86ac5083-3683-4bce-b3f8-c8360c99cc99"}
{"timestamp": "2025-05-28T10:07:34.111445+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 12.73}, "user_id": null, "request_id": "83c1cb61-1fbb-45a8-91fb-e8717b2c2e08"}
{"timestamp": "2025-05-28T10:07:35.271628+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.56}, "user_id": null, "request_id": "033b762d-08ae-441f-bd00-69583ef11a65"}
{"timestamp": "2025-05-28T10:07:36.774758+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 9.2}, "user_id": null, "request_id": "f9c54c90-f75b-433e-bbc5-49976efc92b7"}
{"timestamp": "2025-05-28T10:10:25.976072+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 5.01}, "user_id": null, "request_id": "cff9dcca-40ba-4273-9db3-52d51a758d30"}
{"timestamp": "2025-05-28T10:10:27.470073+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.224s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 223.53}, "user_id": null, "request_id": "d3f4bdff-555d-4fed-b4f3-bf4d2784ac14"}
{"timestamp": "2025-05-28T10:10:28.496192+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.83}, "user_id": null, "request_id": "6eabc1a1-d873-4057-a979-59029efc8371"}
{"timestamp": "2025-05-28T10:10:28.530022+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 16.36}, "user_id": null, "request_id": "f6277783-810a-45f9-9c54-ca328afc2c63"}
{"timestamp": "2025-05-28T10:10:28.530903+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 16.42}, "user_id": null, "request_id": "0c40f443-8588-488c-927d-8c47bb1a68cb"}
{"timestamp": "2025-05-28T10:10:28.531223+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 15.52}, "user_id": null, "request_id": "5f8e036a-cf74-4a64-9c1a-6dc70def6f32"}
{"timestamp": "2025-05-28T10:10:29.416244+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.33}, "user_id": null, "request_id": "c0077660-53a5-4a83-bc99-95909d1ff38b"}
{"timestamp": "2025-05-28T10:10:29.434041+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6.17}, "user_id": null, "request_id": "9a304dda-da34-453d-acf9-c7b891418af6"}
{"timestamp": "2025-05-28T10:10:36.886634+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (4.210s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 4210.14}, "user_id": null, "request_id": "01f19b82-24b5-41f6-82d2-0cb92364300a"}
{"timestamp": "2025-05-28T10:11:59.221942+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (81.690s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 81690.31}, "user_id": null, "request_id": "246221f9-72bf-42c7-bd91-a8d0d99708df"}
{"timestamp": "2025-05-28T10:12:10.480858+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (11.243s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 11243.31}, "user_id": null, "request_id": "6c1872f8-2ca7-46e4-8aea-4f4b63e606b0"}
{"timestamp": "2025-05-28T10:14:06.819278+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/3/sync - 200 (80.709s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/3/sync", "status_code": 200, "duration_ms": 80708.6}, "user_id": null, "request_id": "5c195bc0-e659-40f2-9c9e-26f00189707a"}
{"timestamp": "2025-05-28T10:14:09.543637+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.720s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2720.4}, "user_id": null, "request_id": "a6cecbab-4115-42a9-9031-4ddafef23ef8"}
{"timestamp": "2025-05-28T10:15:22.536550+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/3/sync - 200 (34.993s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/3/sync", "status_code": 200, "duration_ms": 34992.77}, "user_id": null, "request_id": "8e91605b-4037-44d1-a76f-2db470c71dd9"}
{"timestamp": "2025-05-28T10:15:24.449567+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.905s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1904.9}, "user_id": null, "request_id": "d5297d1f-5f7b-4a37-86d0-32ea69d59f0c"}
{"timestamp": "2025-05-28T10:19:39.452442+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (1.172s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 1172.15}, "user_id": null, "request_id": "e75d5b5b-46ab-43d5-9856-fa5ddde73cd6"}
{"timestamp": "2025-05-28T11:17:17.256256+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 5.13}, "user_id": null, "request_id": "ab5961f2-b601-460b-af76-619bd5139871"}
{"timestamp": "2025-05-28T11:17:17.299651+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 11.99}, "user_id": null, "request_id": "a44a79b9-b9fb-4405-8220-7b8388d838b4"}
{"timestamp": "2025-05-28T11:17:17.299887+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 12.39}, "user_id": null, "request_id": "86bd417f-c02a-4a10-8255-864be807f830"}
{"timestamp": "2025-05-28T11:17:17.300351+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.010s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 9.91}, "user_id": null, "request_id": "e704822f-e46a-4cb8-bee4-48bfea026c0c"}
{"timestamp": "2025-05-28T11:17:17.305987+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.18}, "user_id": null, "request_id": "9f9d8e99-5ffc-44db-8996-4b7d29bcb8e6"}
{"timestamp": "2025-05-28T11:17:20.263022+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.243s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 243.2}, "user_id": null, "request_id": "63427f8b-8025-4527-aaf0-02e7c90df86c"}
{"timestamp": "2025-05-28T11:17:21.278229+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.62}, "user_id": null, "request_id": "24789456-4a5a-4756-a3c0-9beede3b80a0"}
{"timestamp": "2025-05-28T11:17:23.923476+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.474s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2474.26}, "user_id": null, "request_id": "1a424bd3-c505-490d-8384-0acdf82fca43"}
{"timestamp": "2025-05-28T11:17:23.924435+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.474s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2474.32}, "user_id": null, "request_id": "c9332699-7f37-4093-abe5-b92c0899f113"}
{"timestamp": "2025-05-28T11:17:23.926346+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.476s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2476.02}, "user_id": null, "request_id": "45741865-5529-4c9f-b3fb-ab97aee44566"}
{"timestamp": "2025-05-28T11:17:24.579371+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 3.85}, "user_id": null, "request_id": "1cd5edf1-ed0b-474e-b6c5-9596871b0534"}
{"timestamp": "2025-05-28T11:17:26.531537+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.937s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1936.62}, "user_id": null, "request_id": "c9a108f0-eca9-44b7-845f-6078618d4362"}
{"timestamp": "2025-05-28T11:17:28.917731+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/3 - 200 (0.499s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/3", "status_code": 200, "duration_ms": 498.99}, "user_id": null, "request_id": "64fef374-7a37-4534-a828-acf9d2023582"}
{"timestamp": "2025-05-28T11:17:28.925766+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.19}, "user_id": null, "request_id": "22795800-d2be-4ee8-93b6-57a35e6794f8"}
{"timestamp": "2025-05-28T11:17:30.017764+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 7.05}, "user_id": null, "request_id": "4638660f-7369-493a-9d58-2e2d9bf87e18"}
{"timestamp": "2025-05-28T11:17:30.042578+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 14.15}, "user_id": null, "request_id": "2f4b2dfa-855d-467b-8bd0-e843a75314a4"}
{"timestamp": "2025-05-28T11:17:30.043340+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 14.1}, "user_id": null, "request_id": "8e6ca23d-877b-4bfe-833f-7d05a3d6141e"}
{"timestamp": "2025-05-28T11:17:30.043631+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 13.95}, "user_id": null, "request_id": "3072b831-06f0-4eeb-9511-ddf8f52ee3ac"}
{"timestamp": "2025-05-28T11:17:31.817555+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.01}, "user_id": null, "request_id": "f9c8b4b9-2af1-4c55-a0bd-0c85e6b30049"}
{"timestamp": "2025-05-28T11:17:34.289267+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.205s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 205.24}, "user_id": null, "request_id": "c2d29597-5846-41cd-ac40-5e6c88653a8f"}
{"timestamp": "2025-05-28T11:17:35.310516+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.62}, "user_id": null, "request_id": "212a3801-3f6f-403f-b1c5-f2f21722b117"}
{"timestamp": "2025-05-28T11:17:35.334459+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 13.78}, "user_id": null, "request_id": "61143f05-54b7-4ae6-a283-e4c37c5b3628"}
{"timestamp": "2025-05-28T11:17:35.335013+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 13.59}, "user_id": null, "request_id": "6584767b-7981-4298-8004-af29769fbe3d"}
{"timestamp": "2025-05-28T11:17:35.335253+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 13.43}, "user_id": null, "request_id": "9c7a4604-9882-402d-b087-e482d3694d53"}
{"timestamp": "2025-05-28T11:17:36.305569+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.66}, "user_id": null, "request_id": "67c24346-7a45-4020-8851-d7d2f6c11c91"}
{"timestamp": "2025-05-28T11:17:36.332304+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.8}, "user_id": null, "request_id": "9f8ba335-abde-4b53-89da-b90721dbf5b6"}
{"timestamp": "2025-05-28T11:17:38.606092+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.56}, "user_id": null, "request_id": "9f3679e1-427f-4d38-b42a-8f5a9198c178"}
{"timestamp": "2025-05-28T11:17:38.632687+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 15.25}, "user_id": null, "request_id": "83209175-2ab8-4ffd-bcd6-afcc52cae4d7"}
{"timestamp": "2025-05-28T11:17:38.633318+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 14.69}, "user_id": null, "request_id": "eac68f2d-d302-4566-8de4-006729e4914e"}
{"timestamp": "2025-05-28T11:17:38.633503+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 15.27}, "user_id": null, "request_id": "fe116bdb-85b0-406f-83f6-fd971644433b"}
{"timestamp": "2025-05-28T11:17:40.560831+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 3.25}, "user_id": null, "request_id": "e22cf7d0-94e1-4787-9057-3e19d61c359a"}
{"timestamp": "2025-05-28T11:18:18.229209+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.204s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 204.0}, "user_id": null, "request_id": "e06599e5-e73d-4ef0-be2d-e2a5b6ef0ffe"}
{"timestamp": "2025-05-28T11:18:19.290378+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.34}, "user_id": null, "request_id": "1df0d021-9141-418a-9e33-9003487a78bb"}
{"timestamp": "2025-05-28T11:18:19.309991+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7.67}, "user_id": null, "request_id": "18b96051-3229-4258-86a3-693fd1643662"}
{"timestamp": "2025-05-28T11:18:19.311085+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 7.62}, "user_id": null, "request_id": "80362a7e-cbd4-4510-8541-ff02e90f8cf7"}
{"timestamp": "2025-05-28T11:18:19.311310+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 7.55}, "user_id": null, "request_id": "a2884aca-03c8-4a55-8f17-9ec133c4704d"}
{"timestamp": "2025-05-28T11:18:20.242869+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.68}, "user_id": null, "request_id": "18c5b80b-043a-462b-ba45-76a90a4ed051"}
{"timestamp": "2025-05-28T11:18:20.262870+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.46}, "user_id": null, "request_id": "aec6a1d9-20fb-431e-9765-a06d70741f84"}
{"timestamp": "2025-05-28T11:18:25.443253+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (1.936s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 1936.21}, "user_id": null, "request_id": "4f042bfa-bbd1-4b94-adf3-4caa4ba004f4"}
{"timestamp": "2025-05-28T11:19:46.239818+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (80.133s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 80132.87}, "user_id": null, "request_id": "bc5e02af-19d3-4698-86d5-d015131afa42"}
{"timestamp": "2025-05-28T11:19:49.782288+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.533s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3532.78}, "user_id": null, "request_id": "b5fb5f27-c7c2-4733-aaf4-f362cc83b605"}
{"timestamp": "2025-05-28T11:21:39.630780+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/4 - 200 (1.432s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/4", "status_code": 200, "duration_ms": 1432.31}, "user_id": null, "request_id": "763aa9c4-c791-4362-a7d9-94bdc98d72fe"}
{"timestamp": "2025-05-28T11:21:39.645832+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 9.31}, "user_id": null, "request_id": "1128eab4-a9a6-4f3d-a35f-c73a3205a2b8"}
{"timestamp": "2025-05-28T11:21:56.399460+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (0.668s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 668.04}, "user_id": null, "request_id": "a8b2a333-3f54-4652-86a3-979995c8be90"}
{"timestamp": "2025-05-28T11:23:08.192898+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (70.845s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 70845.38}, "user_id": null, "request_id": "6d260ef4-02f0-4abd-8f66-7752e42318ca"}
{"timestamp": "2025-05-28T11:23:14.130075+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (5.325s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5325.07}, "user_id": null, "request_id": "ea3eeb65-78d7-4ca4-9c69-47f05b919618"}
{"timestamp": "2025-05-28T11:39:47.978404+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/5 - 200 (12.416s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/5", "status_code": 200, "duration_ms": 12415.89}, "user_id": null, "request_id": "64d553fd-843e-4f3f-8770-1df2fade3a60"}
{"timestamp": "2025-05-28T11:39:47.990810+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.010s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 10.15}, "user_id": null, "request_id": "d00a3654-41cc-419a-b9f0-e208356a2117"}
{"timestamp": "2025-05-28T11:39:48.000579+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/5 - 200 (0.023s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/5", "status_code": 200, "duration_ms": 22.67}, "user_id": null, "request_id": "912965b6-4a57-460a-ba10-e0f87202c38d"}
{"timestamp": "2025-05-28T11:39:48.010061+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.53}, "user_id": null, "request_id": "47f57602-99b7-45d4-ad82-c1f1eea2ecd5"}
{"timestamp": "2025-05-28T11:39:48.019683+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.81}, "user_id": null, "request_id": "d8b9bf61-9520-4dd9-86d0-9d01e13f6635"}
{"timestamp": "2025-05-28T11:39:48.031527+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 12.22}, "user_id": null, "request_id": "37830e9a-8bde-485e-8f8c-c88ee338e456"}
{"timestamp": "2025-05-28T11:39:48.031899+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 11.53}, "user_id": null, "request_id": "ded337a6-2d77-4711-9651-f23d7932cda5"}
{"timestamp": "2025-05-28T11:39:48.035415+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3.05}, "user_id": null, "request_id": "653a8875-5bcb-430e-ad40-cec2c3e24ecb"}
{"timestamp": "2025-05-28T11:39:54.920918+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.23}, "user_id": null, "request_id": "4727db25-01f7-4b70-a7db-9bac8c01517f"}
{"timestamp": "2025-05-28T11:39:58.218612+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.221s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 221.32}, "user_id": null, "request_id": "a028fad8-510d-4a21-855d-e2c96618c7f2"}
{"timestamp": "2025-05-28T11:39:59.440556+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.05}, "user_id": null, "request_id": "fb2b541b-c90d-408b-be0e-6b3a4682fc7c"}
{"timestamp": "2025-05-28T11:39:59.755824+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 15.69}, "user_id": null, "request_id": "f5a6ad52-2825-4429-a95d-4dae9c3abc4e"}
{"timestamp": "2025-05-28T11:39:59.756494+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 15.64}, "user_id": null, "request_id": "7bd2d5c3-20b8-48ea-8493-cf4fdc7eefd0"}
{"timestamp": "2025-05-28T11:39:59.756891+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 15.93}, "user_id": null, "request_id": "5487b469-70ba-45b1-bd3b-a214d7ce4a99"}
{"timestamp": "2025-05-28T11:40:02.678511+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.69}, "user_id": null, "request_id": "2def1ba8-71cb-42dc-a9d8-8a52bd5098f1"}
{"timestamp": "2025-05-28T11:40:05.154236+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.217s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 217.33}, "user_id": null, "request_id": "f97459ff-0231-403e-bca0-d65bc4694bd3"}
{"timestamp": "2025-05-28T11:40:06.167425+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.95}, "user_id": null, "request_id": "bb99a227-1a6f-4101-b8b6-e2cfae43aa12"}
{"timestamp": "2025-05-28T11:40:06.191391+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.5}, "user_id": null, "request_id": "6cc35c20-f88e-4de7-ae61-2944bac50ae1"}
{"timestamp": "2025-05-28T11:40:06.196633+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 6.45}, "user_id": null, "request_id": "92fba291-fbbb-44b5-901c-44d68f369dd5"}
{"timestamp": "2025-05-28T11:40:06.196891+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 6.6}, "user_id": null, "request_id": "576c6bc2-b3b8-42ee-a2c4-efca1f360bc6"}
{"timestamp": "2025-05-28T11:40:08.375751+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 7.16}, "user_id": null, "request_id": "530c0f36-c357-491a-9906-82579cf3fa8e"}
{"timestamp": "2025-05-28T11:40:08.402073+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6.88}, "user_id": null, "request_id": "8f16ffbb-066e-44c3-8fd9-9e843cac2b5a"}
{"timestamp": "2025-05-28T11:40:19.659268+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (7.776s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 7775.77}, "user_id": null, "request_id": "4ed21dd2-5716-4172-9af7-4504332701db"}
{"timestamp": "2025-05-28T11:41:25.636882+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (62.180s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 62180.26}, "user_id": null, "request_id": "ae6b2e11-a74b-4988-9186-267cb327e8cb"}
{"timestamp": "2025-05-28T11:41:28.334287+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.690s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2689.76}, "user_id": null, "request_id": "06ad6e69-428e-4a28-9052-96a1350a1f14"}
{"timestamp": "2025-05-28T11:44:49.716703+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/6 - 200 (2.198s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/6", "status_code": 200, "duration_ms": 2198.09}, "user_id": null, "request_id": "587c781b-79c6-40b8-ae4d-b62e69300798"}
{"timestamp": "2025-05-28T11:44:49.784131+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3.44}, "user_id": null, "request_id": "9b6126c5-fd12-437f-88e1-a74e076f362b"}
{"timestamp": "2025-05-28T11:44:52.496458+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 6.84}, "user_id": null, "request_id": "abc0d7ab-053d-4212-a591-e46524fe826d"}
{"timestamp": "2025-05-28T11:44:52.533810+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.019s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 18.62}, "user_id": null, "request_id": "29aae571-d924-4c08-9c49-70508497e82b"}
{"timestamp": "2025-05-28T11:44:52.534259+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.019s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 19.03}, "user_id": null, "request_id": "b85b6b09-b969-4946-aec6-15da8c1136e3"}
{"timestamp": "2025-05-28T11:44:52.534815+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.020s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 19.53}, "user_id": null, "request_id": "3e0b23ea-a220-4cb4-9305-3c5cd24edfa5"}
{"timestamp": "2025-05-28T11:44:53.870331+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.94}, "user_id": null, "request_id": "e0741db6-e606-44b7-a2be-ab22beba5018"}
{"timestamp": "2025-05-28T11:44:55.934460+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.237s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 236.52}, "user_id": null, "request_id": "64436a62-d1a9-4207-a6c2-470909ab41e0"}
{"timestamp": "2025-05-28T11:44:56.941309+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.68}, "user_id": null, "request_id": "ce4e0e9b-5ff4-4429-b2d4-dc18cce2d71d"}
{"timestamp": "2025-05-28T11:44:56.960760+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.3}, "user_id": null, "request_id": "e667dfb9-a7bd-4e29-8aad-cd197b16055c"}
{"timestamp": "2025-05-28T11:44:56.961065+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 5.59}, "user_id": null, "request_id": "5d30fd81-1a82-470b-8aa1-ee625819a8e2"}
{"timestamp": "2025-05-28T11:44:56.961182+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 6.2}, "user_id": null, "request_id": "408cc414-3cf2-464f-b1cc-29a11639ea9d"}
{"timestamp": "2025-05-28T11:44:58.556068+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.58}, "user_id": null, "request_id": "926eb17c-75dd-4107-8cf8-70c489f6a547"}
{"timestamp": "2025-05-28T11:44:58.572050+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.32}, "user_id": null, "request_id": "3b2b9940-6d84-4e10-a610-7c401197bacd"}
{"timestamp": "2025-05-28T11:45:09.363985+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (7.118s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 7117.77}, "user_id": null, "request_id": "1390d40c-a3cc-4685-83df-0f63450c7a6b"}
{"timestamp": "2025-05-28T11:46:57.246426+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (105.952s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 105952.05}, "user_id": null, "request_id": "0c8f7e42-115d-4c3e-9b56-705ef80c57f1"}
{"timestamp": "2025-05-28T11:46:59.558383+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.303s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2303.08}, "user_id": null, "request_id": "3b854fe6-8acf-4f8b-ad0a-46910f4ba33e"}
{"timestamp": "2025-05-28T11:58:03.178147+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/7/sync - 200 (116.509s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/7/sync", "status_code": 200, "duration_ms": 116509.33}, "user_id": null, "request_id": "f9b970a2-5981-48a2-bbd1-6d03936e74ae"}
{"timestamp": "2025-05-28T11:58:11.578499+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (8.395s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8395.48}, "user_id": null, "request_id": "d3d687c0-a77e-486e-80fb-b60cda22254b"}
{"timestamp": "2025-05-28T12:15:47.482754+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/7/sync - 401 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/7/sync", "status_code": 401, "duration_ms": 5.87}, "user_id": null, "request_id": "ff312709-be8a-414d-9845-7afab332df99"}
{"timestamp": "2025-05-28T12:29:57.364025+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/7 - 401 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/7", "status_code": 401, "duration_ms": 8.98}, "user_id": null, "request_id": "0590712e-1511-463c-a192-fba47d761eb5"}
{"timestamp": "2025-05-28T12:29:59.363798+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 7.7}, "user_id": null, "request_id": "fd29452d-cdea-4b37-bfea-ea17044239f4"}
{"timestamp": "2025-05-28T12:29:59.392904+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 6.05}, "user_id": null, "request_id": "dd8beb35-c03e-4769-9b8f-f9d7bebd4964"}
{"timestamp": "2025-05-28T12:29:59.394828+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 7.23}, "user_id": null, "request_id": "09b4aeab-be34-48bf-ac38-e6462600617c"}
{"timestamp": "2025-05-28T12:29:59.397324+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 8.45}, "user_id": null, "request_id": "81a015cb-86db-4e5e-8345-4cde3b34b335"}
{"timestamp": "2025-05-28T12:29:59.404287+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.74}, "user_id": null, "request_id": "e2f1574b-6449-4faa-a39a-3662596edbbd"}
{"timestamp": "2025-05-28T12:30:01.957243+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.253s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 252.85}, "user_id": null, "request_id": "77e20c70-429a-49b9-959c-066542349b07"}
{"timestamp": "2025-05-28T12:30:02.969467+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.0}, "user_id": null, "request_id": "051bbac9-d33a-4616-bde6-728cfae9684d"}
{"timestamp": "2025-05-28T12:30:06.197741+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.214s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3213.58}, "user_id": null, "request_id": "61b6bd34-80d5-4974-a87a-3dec4e96b41e"}
{"timestamp": "2025-05-28T12:30:06.268168+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.071s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 71.26}, "user_id": null, "request_id": "4bf8ddd9-8f59-4c12-911c-6c2c0590fec0"}
{"timestamp": "2025-05-28T12:30:06.269067+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.072s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 72.07}, "user_id": null, "request_id": "c5893ce5-8ee0-4e76-b91b-821daed010b7"}
{"timestamp": "2025-05-28T12:30:06.867868+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.64}, "user_id": null, "request_id": "a25fa77c-a814-4e4b-8b87-5da031fe0eb7"}
{"timestamp": "2025-05-28T12:30:06.884321+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 4.74}, "user_id": null, "request_id": "b3796224-82c1-4715-a1c7-4f020afaec22"}
{"timestamp": "2025-05-28T12:30:21.375221+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (14.488s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 14487.84}, "user_id": null, "request_id": "e9a495dd-df59-4971-8d86-0b02210df4b9"}
{"timestamp": "2025-05-28T12:30:25.925528+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/7 - 200 (0.433s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/7", "status_code": 200, "duration_ms": 432.86}, "user_id": null, "request_id": "bc238cd1-d0a7-4847-beff-7e564fede0e5"}
{"timestamp": "2025-05-28T12:30:25.941513+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 10.73}, "user_id": null, "request_id": "a45442fb-4ac6-45d3-b7c9-68bab8debec5"}
{"timestamp": "2025-05-28T12:30:27.499607+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.4}, "user_id": null, "request_id": "507d9ed0-716f-4688-a1c2-2e581eafd7c4"}
{"timestamp": "2025-05-28T12:30:27.529567+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 13.85}, "user_id": null, "request_id": "7b461602-e8d4-4207-8881-68e0a090d160"}
{"timestamp": "2025-05-28T12:30:27.534074+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 5.85}, "user_id": null, "request_id": "e92e3092-e43a-4444-a741-68aacc382378"}
{"timestamp": "2025-05-28T12:30:27.534227+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 5.94}, "user_id": null, "request_id": "7e17153a-7471-4652-ab25-0a8cece14621"}
{"timestamp": "2025-05-28T12:30:28.830192+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.86}, "user_id": null, "request_id": "4fc1b34b-d414-40e8-911d-6d451963b640"}
{"timestamp": "2025-05-28T12:30:31.067568+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.203s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 202.7}, "user_id": null, "request_id": "ef74e3ce-d891-462c-b885-286da9a971ca"}
{"timestamp": "2025-05-28T12:30:32.082001+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.12}, "user_id": null, "request_id": "d42caec6-b694-4309-84a3-1091e46960a7"}
{"timestamp": "2025-05-28T12:30:32.108246+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 12.95}, "user_id": null, "request_id": "769ce919-1ace-41e2-8980-003946ceefc2"}
{"timestamp": "2025-05-28T12:30:32.108549+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 11.67}, "user_id": null, "request_id": "91171da6-c8f8-401b-b00f-965b9fd55c92"}
{"timestamp": "2025-05-28T12:30:32.108866+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 11.86}, "user_id": null, "request_id": "3d8a0bbf-a824-4576-9b75-78ef9ad887f7"}
{"timestamp": "2025-05-28T12:30:34.009721+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.69}, "user_id": null, "request_id": "0bcbd08a-6915-4459-a190-1ca803f9639d"}
{"timestamp": "2025-05-28T12:30:34.030003+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.43}, "user_id": null, "request_id": "04ce9de4-b819-4a8f-a54d-2a5d21ffcf65"}
{"timestamp": "2025-05-28T12:30:46.599738+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (9.094s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 9093.67}, "user_id": null, "request_id": "3ed4b7b6-2144-455b-911f-d15a705ad664"}
{"timestamp": "2025-05-28T12:32:45.277846+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (117.213s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 117213.23}, "user_id": null, "request_id": "dfecf073-1adf-4ac2-9cf8-a565f5257444"}
{"timestamp": "2025-05-28T12:32:58.012282+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (12.723s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 12723.35}, "user_id": null, "request_id": "fa936eaa-63fa-4249-a5c9-8e1d1452d2fc"}
{"timestamp": "2025-05-28T12:36:13.833365+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/8/sync - 200 (103.981s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/8/sync", "status_code": 200, "duration_ms": 103980.69}, "user_id": null, "request_id": "d591d1c4-b8f4-47d3-9d9c-2d9af741892f"}
{"timestamp": "2025-05-28T12:36:16.926093+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.089s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3088.93}, "user_id": null, "request_id": "add663b5-2560-4ff3-9fe8-7365b4dabb06"}
{"timestamp": "2025-05-28T12:38:43.135088+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/8/sync - 200 (42.844s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/8/sync", "status_code": 200, "duration_ms": 42844.49}, "user_id": null, "request_id": "54e02b3c-bae3-4534-89ce-d2fdd8c8b8a2"}
{"timestamp": "2025-05-28T12:38:50.346134+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (7.205s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7204.86}, "user_id": null, "request_id": "08eb9cf5-6162-42cf-9bcd-0e0514c5d019"}
{"timestamp": "2025-05-28T12:43:52.034191+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/8 - 404 (0.020s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/8", "status_code": 404, "duration_ms": 19.78}, "user_id": null, "request_id": "12411c3c-e6a0-4766-8e5a-01657da45f74"}
{"timestamp": "2025-05-28T12:43:56.706578+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/8 - 404 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/8", "status_code": 404, "duration_ms": 5.91}, "user_id": null, "request_id": "27ab3ab0-6e7c-40b7-8629-c2633dd5986b"}
{"timestamp": "2025-05-28T12:43:59.716625+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.07}, "user_id": null, "request_id": "555ed409-c8e2-438d-8e16-7af9abdd340a"}
{"timestamp": "2025-05-28T12:43:59.732094+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6.53}, "user_id": null, "request_id": "7df46621-ccff-4c6d-98d4-e9b0ab61b4fe"}
{"timestamp": "2025-05-28T12:44:01.093474+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 3.61}, "user_id": null, "request_id": "10d89cfa-259d-4422-af4e-28f73196a397"}
{"timestamp": "2025-05-28T12:44:01.116762+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 15.37}, "user_id": null, "request_id": "af0f9c6a-faba-406f-bf6a-552459c30d51"}
{"timestamp": "2025-05-28T12:44:01.121403+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.019s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 18.86}, "user_id": null, "request_id": "09276c45-e1da-4c6b-a615-0d9ff4f7ae88"}
{"timestamp": "2025-05-28T12:44:01.121927+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.019s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 18.6}, "user_id": null, "request_id": "aea3fb74-9f81-4fe0-9025-856a50d043d4"}
{"timestamp": "2025-05-28T12:44:02.644841+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.21}, "user_id": null, "request_id": "c45a38d2-5b0f-4ec8-ade9-fe1ce8d934be"}
{"timestamp": "2025-05-28T12:44:04.832810+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.224s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 223.81}, "user_id": null, "request_id": "288007ed-b2b8-41cd-b20d-98a66c2dffd6"}
{"timestamp": "2025-05-28T12:44:05.843375+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.8}, "user_id": null, "request_id": "255fe9a9-cd90-4a2b-bbea-a7cd72f352fd"}
{"timestamp": "2025-05-28T12:44:05.869366+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 15.35}, "user_id": null, "request_id": "df620874-8a2c-4428-bb46-47ae0e86d83b"}
{"timestamp": "2025-05-28T12:44:05.869910+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 15.81}, "user_id": null, "request_id": "f7cd72b9-c678-4492-b293-9962aaac925b"}
{"timestamp": "2025-05-28T12:44:05.870115+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 15.95}, "user_id": null, "request_id": "90a1f487-46e0-4ada-903a-7793f5c6c0bb"}
{"timestamp": "2025-05-28T12:44:28.725926+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.31}, "user_id": null, "request_id": "b93c939a-5380-4a3a-bd3b-bbea96ab3e9a"}
{"timestamp": "2025-05-28T12:44:30.211642+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.205s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 205.08}, "user_id": null, "request_id": "e0f6081f-980a-4072-9996-b96a39f4877e"}
{"timestamp": "2025-05-28T12:44:31.222049+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.66}, "user_id": null, "request_id": "d611aa9c-155b-4d7c-88fd-d59ba0de8b78"}
{"timestamp": "2025-05-28T12:44:31.248507+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 11.94}, "user_id": null, "request_id": "1eac4b81-35cd-4a02-b1b8-3df5e1469938"}
{"timestamp": "2025-05-28T12:44:31.249110+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 11.62}, "user_id": null, "request_id": "332a631e-0f0c-4658-877d-e7b053f38b67"}
{"timestamp": "2025-05-28T12:44:31.249330+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 11.01}, "user_id": null, "request_id": "8c8ef5df-015a-48ac-b9df-07e9b6d605b9"}
{"timestamp": "2025-05-28T12:44:32.240738+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.74}, "user_id": null, "request_id": "65987335-5bfb-41ac-b2e7-5258a2f42152"}
{"timestamp": "2025-05-28T12:44:32.255896+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3.96}, "user_id": null, "request_id": "d7b364eb-dda5-450d-850b-ceaea97835ae"}
{"timestamp": "2025-05-28T12:44:44.971766+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (9.400s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 9400.06}, "user_id": null, "request_id": "42c8400c-85df-4307-83d6-5fd3768331dd"}
{"timestamp": "2025-05-28T12:46:52.279894+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (125.823s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 125822.66}, "user_id": null, "request_id": "d92ade21-d116-4202-a298-f4009fb6fa27"}
{"timestamp": "2025-05-28T12:47:02.248712+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (9.957s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 9956.68}, "user_id": null, "request_id": "e6a0bddf-9471-4786-b9c8-ede80b88929d"}
{"timestamp": "2025-05-28T12:49:02.781506+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/9/sync - 200 (106.276s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/9/sync", "status_code": 200, "duration_ms": 106276.1}, "user_id": null, "request_id": "b406ef59-df7e-4e40-a966-9ec8f4e7d1b3"}
{"timestamp": "2025-05-28T12:49:04.798486+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2010.79}, "user_id": null, "request_id": "419cf393-2df4-4648-bf28-391cd037fb99"}
{"timestamp": "2025-05-28T12:59:35.962530+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/9 - 404 (0.021s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/9", "status_code": 404, "duration_ms": 21.22}, "user_id": null, "request_id": "40b73859-c85f-4d78-a3da-78e2e7f34c44"}
{"timestamp": "2025-05-28T12:59:38.572307+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 7.54}, "user_id": null, "request_id": "d298915d-5665-4d32-b20c-6226500676de"}
{"timestamp": "2025-05-28T12:59:38.595453+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6.85}, "user_id": null, "request_id": "566a6b5b-8e19-40ae-be23-60aa22c0a128"}
{"timestamp": "2025-05-28T12:59:39.787685+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.98}, "user_id": null, "request_id": "34291fa4-99e1-4656-9bfa-6b0b7613478c"}
{"timestamp": "2025-05-28T12:59:39.811512+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 12.98}, "user_id": null, "request_id": "0b64c1d8-ff0f-4d10-a04c-fb4a9ced26a8"}
{"timestamp": "2025-05-28T12:59:39.832873+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.032s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 31.76}, "user_id": null, "request_id": "3f6e285c-3ec5-47a1-84ae-239e40a0fb7c"}
{"timestamp": "2025-05-28T12:59:39.833150+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.032s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 31.85}, "user_id": null, "request_id": "fd913ec9-0ecc-4dc8-bedb-bee77bc1b354"}
{"timestamp": "2025-05-28T12:59:41.388932+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 5.28}, "user_id": null, "request_id": "2684dd73-53bb-4f4a-9017-d3c791dc4c27"}
{"timestamp": "2025-05-28T12:59:43.847066+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.229s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 228.92}, "user_id": null, "request_id": "e4cb1f62-2817-44bd-a27d-b1d342851ed3"}
{"timestamp": "2025-05-28T12:59:44.949805+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.07}, "user_id": null, "request_id": "d13f3a93-3ef6-4559-b851-39da60371a00"}
{"timestamp": "2025-05-28T12:59:44.969587+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.58}, "user_id": null, "request_id": "53031fca-d979-48ca-aae3-2b6c553b8a75"}
{"timestamp": "2025-05-28T12:59:44.976006+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 9.31}, "user_id": null, "request_id": "16ecd758-331d-4096-be2d-63f7f2ce13d1"}
{"timestamp": "2025-05-28T12:59:44.976568+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 7.21}, "user_id": null, "request_id": "8639b01e-9744-4de9-b778-c6845115dd21"}
{"timestamp": "2025-05-28T12:59:47.030151+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.6}, "user_id": null, "request_id": "d9bc87ed-46fe-4601-87e5-3f89bfce778b"}
{"timestamp": "2025-05-28T12:59:47.056086+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7.27}, "user_id": null, "request_id": "43cc2d8d-7753-46c2-8998-da184049bb11"}
{"timestamp": "2025-05-28T12:59:54.290710+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (4.054s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 4053.74}, "user_id": null, "request_id": "a269ef8f-2048-483c-b6b4-2604c7b00c15"}
{"timestamp": "2025-05-28T13:00:43.816956+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (48.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 48000.91}, "user_id": null, "request_id": "ba6f6e2b-8d34-4f00-b21a-05519d850189"}
{"timestamp": "2025-05-28T13:00:45.711360+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.868s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1868.13}, "user_id": null, "request_id": "1adb1442-0bfb-4450-a3d5-b653695c31eb"}
{"timestamp": "2025-05-28T13:02:45.703319+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 6.39}, "user_id": null, "request_id": "34771178-9d3e-48f9-a037-1fd21bb3fa35"}
{"timestamp": "2025-05-28T13:02:56.890618+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (11.170s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 11169.76}, "user_id": null, "request_id": "5c1e8613-28f1-4caa-9606-9e5eae65b18d"}
{"timestamp": "2025-05-28T13:02:56.890899+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.55}, "user_id": null, "request_id": "8c9c1406-910f-4ef4-806e-82e476e57991"}
{"timestamp": "2025-05-28T13:03:01.181127+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (4.276s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4275.75}, "user_id": null, "request_id": "6ba063d3-d4fc-420c-bd8b-05ab2ad7ec54"}
{"timestamp": "2025-05-28T13:03:01.184853+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 9.3}, "user_id": null, "request_id": "e45e9c04-6c00-4368-882c-6aa96d98449e"}
{"timestamp": "2025-05-28T13:03:03.361152+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.156s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2156.12}, "user_id": null, "request_id": "9cf48cea-ed01-4bbd-bbdf-2b6334a08af1"}
{"timestamp": "2025-05-28T13:03:03.380991+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.021s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 20.67}, "user_id": null, "request_id": "23e861b6-296a-47c1-aea7-c3dd54a26555"}
{"timestamp": "2025-05-28T13:03:03.382019+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.021s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 21.01}, "user_id": null, "request_id": "4018397d-1601-4540-83f8-6458a0ecefd6"}
{"timestamp": "2025-05-28T13:03:06.717879+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.87}, "user_id": null, "request_id": "abd91b5a-ef31-4e56-80e2-1bc0ace18824"}
{"timestamp": "2025-05-28T13:03:19.898231+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (13.170s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 13170.23}, "user_id": null, "request_id": "786ff86e-1897-4885-a899-00e9f3b267e7"}
{"timestamp": "2025-05-28T13:03:36.437542+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/10 - 200 (2.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/10", "status_code": 200, "duration_ms": 2004.79}, "user_id": null, "request_id": "69d885d2-6463-406c-adc7-16c0bf278d6e"}
{"timestamp": "2025-05-28T13:03:36.451604+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.34}, "user_id": null, "request_id": "56cc9aa3-ca38-4471-b5aa-4f9aa0a29b71"}
{"timestamp": "2025-05-28T13:03:42.992366+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (1.732s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 1731.95}, "user_id": null, "request_id": "cd5bd6ea-17b8-4e46-9dd0-85f8ecafc566"}
{"timestamp": "2025-05-28T13:05:24.622053+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (100.242s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 100242.17}, "user_id": null, "request_id": "ba2baa7c-8592-456c-b606-303afdceb78d"}
{"timestamp": "2025-05-28T13:05:33.891551+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (9.204s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 9204.07}, "user_id": null, "request_id": "2d1f8921-5cd0-41a6-ad64-718c0ef9c99e"}
{"timestamp": "2025-05-28T13:08:09.891030+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/11/sync - 200 (120.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/11/sync", "status_code": 200, "duration_ms": 120012.45}, "user_id": null, "request_id": "f5d41443-fcaa-4576-9a35-c0b394c97905"}
{"timestamp": "2025-05-28T13:08:14.554251+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (4.659s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4659.48}, "user_id": null, "request_id": "5b760af5-d24e-4880-9ebd-3aa80a47e6ab"}
{"timestamp": "2025-05-28T13:11:59.256198+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.24}, "user_id": null, "request_id": "08bd4ffc-8075-49eb-8385-4b10233f7ac7"}
{"timestamp": "2025-05-28T13:11:59.294777+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.024s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 24.35}, "user_id": null, "request_id": "7c9f3c73-110b-4f86-95a3-40d0aa20c3db"}
{"timestamp": "2025-05-28T13:12:03.358715+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.87}, "user_id": null, "request_id": "16442e93-9f41-44d4-8a71-011758e6f387"}
{"timestamp": "2025-05-28T13:12:03.393868+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.018s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 17.59}, "user_id": null, "request_id": "3b5abbee-1aaa-4906-b632-fed1d42ebfcd"}
{"timestamp": "2025-05-28T13:12:03.394589+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.018s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 17.51}, "user_id": null, "request_id": "fcf13215-7998-4c29-b9be-9ebff82b9cc3"}
{"timestamp": "2025-05-28T13:12:03.394810+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 16.36}, "user_id": null, "request_id": "3109b8b9-2548-4122-acb8-e4939f1cd9de"}
{"timestamp": "2025-05-28T13:12:05.210657+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.23}, "user_id": null, "request_id": "38faaca8-260d-4d48-8dde-05369271b91b"}
{"timestamp": "2025-05-28T13:12:07.388755+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.225s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 224.57}, "user_id": null, "request_id": "e2ae2349-01f3-4ae8-bdf8-78a00f6b03d5"}
{"timestamp": "2025-05-28T13:12:08.465522+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.87}, "user_id": null, "request_id": "9a1fb509-34f5-4b73-9007-89522a4685db"}
{"timestamp": "2025-05-28T13:12:08.493747+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.61}, "user_id": null, "request_id": "d904c30a-df10-4d0b-8379-571fe6417925"}
{"timestamp": "2025-05-28T13:12:08.493899+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 8.01}, "user_id": null, "request_id": "ad410c05-1858-4558-8173-550f245e8f96"}
{"timestamp": "2025-05-28T13:12:08.494101+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 5.43}, "user_id": null, "request_id": "a85003ed-9562-40b3-8aaf-b472476b358c"}
{"timestamp": "2025-05-28T13:12:16.236126+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.0}, "user_id": null, "request_id": "d02202e5-d1ca-4a86-839d-7219a3b31d20"}
{"timestamp": "2025-05-28T13:12:16.250795+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.47}, "user_id": null, "request_id": "f4974ba8-1e35-43d7-a330-dc941c35421c"}
{"timestamp": "2025-05-28T13:12:21.694512+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (2.464s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 2464.04}, "user_id": null, "request_id": "c2b620b7-837a-4258-b5fa-396b091b116f"}
{"timestamp": "2025-05-28T13:14:23.506756+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (120.329s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 120329.03}, "user_id": null, "request_id": "a96fecfa-ece3-42b4-8f09-f425adcd40e8"}
{"timestamp": "2025-05-28T13:14:33.526634+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (10.016s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 10016.12}, "user_id": null, "request_id": "3215526a-355d-4c03-9323-d15086600e3b"}
{"timestamp": "2025-05-28T13:14:33.528028+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.63}, "user_id": null, "request_id": "fde585c6-a017-4236-aebd-8008e9c72c2d"}
{"timestamp": "2025-05-28T13:14:40.395169+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (6.726s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6725.81}, "user_id": null, "request_id": "f312fb2c-add3-4673-9dab-37f87e3ca2ac"}
{"timestamp": "2025-05-28T13:15:53.741597+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/12/sync - 200 (54.764s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/12/sync", "status_code": 200, "duration_ms": 54763.88}, "user_id": null, "request_id": "81f38467-c923-4b52-ab71-58f5efed4230"}
{"timestamp": "2025-05-28T13:16:06.046681+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (12.283s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 12282.81}, "user_id": null, "request_id": "a9cad34d-e0cd-4f7b-82c4-aa66e18495f6"}
{"timestamp": "2025-05-28T13:37:28.608770+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 8.24}, "user_id": null, "request_id": "1af94119-c06c-4a7f-8c9f-7f2f6b61273a"}
{"timestamp": "2025-05-28T13:37:28.652420+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 28.55}, "user_id": null, "request_id": "4ff97de6-81ab-44d6-bdff-a7201327b2b6"}
{"timestamp": "2025-05-28T13:37:28.653177+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.027s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 27.1}, "user_id": null, "request_id": "72000a7f-3959-4bfe-a031-007e3671511b"}
{"timestamp": "2025-05-28T13:37:28.653496+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 5.74}, "user_id": null, "request_id": "08935ffa-18e6-491a-8158-9165b025c4c9"}
{"timestamp": "2025-05-28T13:37:28.659640+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.25}, "user_id": null, "request_id": "4c63b680-afef-4143-a6b7-ef6bb1361e69"}
{"timestamp": "2025-05-28T13:37:31.761865+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 401 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 401, "duration_ms": 6.75}, "user_id": null, "request_id": "6c86b1e2-356a-4b65-9c22-beac74003281"}
{"timestamp": "2025-05-28T13:37:32.638524+00:00", "level": "INFO", "logger": "api", "message": "GET /register - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/register", "status_code": 200, "duration_ms": 7.36}, "user_id": null, "request_id": "aae80416-743c-49da-9251-a54001993264"}
{"timestamp": "2025-05-28T13:37:40.830235+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/register - 201 (0.240s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/register", "status_code": 201, "duration_ms": 239.88}, "user_id": null, "request_id": "e1aa3045-9455-4eb5-aaf6-bbeec1408d07"}
{"timestamp": "2025-05-28T13:37:41.851027+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.36}, "user_id": null, "request_id": "abddfd91-ab79-4492-aed4-1023c173730e"}
{"timestamp": "2025-05-28T13:37:41.972870+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.031s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 31.35}, "user_id": null, "request_id": "99bd4706-d66c-40a3-a69a-268ac7d43f7a"}
{"timestamp": "2025-05-28T13:37:41.973636+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.031s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 31.4}, "user_id": null, "request_id": "452ff6af-edb6-43cf-b4cb-885fa420628b"}
{"timestamp": "2025-05-28T13:37:41.973881+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 28.67}, "user_id": null, "request_id": "c1e4509b-fb8e-46e5-bd96-5b76ec12df4b"}
{"timestamp": "2025-05-28T13:37:44.155756+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 4.15}, "user_id": null, "request_id": "d6ee6fa5-0379-49dc-ae06-824b8a6967ea"}
{"timestamp": "2025-05-28T13:37:44.174472+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.73}, "user_id": null, "request_id": "529d2865-eec8-40bd-bfad-d937ae7f8c97"}
{"timestamp": "2025-05-28T13:37:47.938220+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (0.602s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 602.19}, "user_id": null, "request_id": "f49d8d6b-7853-4b2f-8232-7475a9c28f17"}
{"timestamp": "2025-05-28T13:38:30.813639+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (41.633s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 41632.65}, "user_id": null, "request_id": "4839b2fc-d611-4179-93f0-c0af92dc1180"}
{"timestamp": "2025-05-28T13:38:37.575340+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (6.755s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6755.24}, "user_id": null, "request_id": "31fed80a-f4ec-4d92-9560-81e05d547adc"}
{"timestamp": "2025-05-28T13:38:37.576838+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.71}, "user_id": null, "request_id": "26db66d5-7955-4655-ba90-a67a76182e07"}
{"timestamp": "2025-05-28T13:38:40.971559+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.118s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3117.7}, "user_id": null, "request_id": "3ea517ec-ee1a-442f-b0be-02a5b5ac17fa"}
{"timestamp": "2025-05-28T13:45:03.435835+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 5.24}, "user_id": null, "request_id": "0c87056b-fa10-43da-8e7a-b05fb797051c"}
{"timestamp": "2025-05-28T13:45:03.474203+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.024s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 23.89}, "user_id": null, "request_id": "75d39bca-bd94-4d1f-a8cf-f59177f2f96e"}
{"timestamp": "2025-05-28T13:45:03.474850+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.023s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 22.63}, "user_id": null, "request_id": "365b1d90-2b52-478a-9c9e-684de160ca13"}
{"timestamp": "2025-05-28T13:45:03.477269+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.025s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 25.42}, "user_id": null, "request_id": "e695d6ea-05f2-4444-8c30-c4ad061d804b"}
{"timestamp": "2025-05-28T13:45:03.484846+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 3.75}, "user_id": null, "request_id": "e24e4189-ff0c-46bf-91f1-46f7f5f82ae0"}
{"timestamp": "2025-05-28T13:45:05.757593+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 401 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 401, "duration_ms": 5.76}, "user_id": null, "request_id": "f113f31c-32b1-4797-9adc-ca35528a5c66"}
{"timestamp": "2025-05-28T13:45:07.932191+00:00", "level": "INFO", "logger": "api", "message": "GET /register - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/register", "status_code": 200, "duration_ms": 3.12}, "user_id": null, "request_id": "db6242c9-4e7b-4966-846f-9d9614a13044"}
{"timestamp": "2025-05-28T13:45:17.729899+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/register - 201 (0.256s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/register", "status_code": 201, "duration_ms": 255.59}, "user_id": null, "request_id": "b2c9f673-b91f-438d-bd86-ddd431e0ea5f"}
{"timestamp": "2025-05-28T13:45:18.740211+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.74}, "user_id": null, "request_id": "04670aa6-9c10-44c0-84ba-7a1c974c2e3e"}
{"timestamp": "2025-05-28T13:45:18.772075+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 17.47}, "user_id": null, "request_id": "51bb6fbb-8b0d-46bf-8721-49a323c05a6c"}
{"timestamp": "2025-05-28T13:45:18.772933+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.018s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 17.9}, "user_id": null, "request_id": "e97a8b18-f85d-4b93-b4a3-b98d959ddfbd"}
{"timestamp": "2025-05-28T13:45:18.773199+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 17.07}, "user_id": null, "request_id": "a8b39cfe-421f-4e1d-ad1c-0f8f9e199df1"}
{"timestamp": "2025-05-28T13:45:20.824037+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.15}, "user_id": null, "request_id": "f5bf2de8-0914-4180-a23d-e110ae84661a"}
{"timestamp": "2025-05-28T13:45:23.159917+00:00", "level": "INFO", "logger": "api", "message": "GET /register - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/register", "status_code": 200, "duration_ms": 0.95}, "user_id": null, "request_id": "30e76f17-c9ef-4b6c-bcca-43eb42aac990"}
{"timestamp": "2025-05-28T13:45:30.198895+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/register - 201 (0.224s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/register", "status_code": 201, "duration_ms": 223.56}, "user_id": null, "request_id": "de37888b-1a0f-4dcd-a066-0936567adbe9"}
{"timestamp": "2025-05-28T13:45:31.218914+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 3.52}, "user_id": null, "request_id": "cc59463b-a37b-4433-836f-5df3a0b84164"}
{"timestamp": "2025-05-28T13:45:31.248643+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 13.68}, "user_id": null, "request_id": "c9151733-3955-4fb4-ab2b-4efe0489bf9b"}
{"timestamp": "2025-05-28T13:45:31.249843+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 14.72}, "user_id": null, "request_id": "c35779f9-a960-428f-9040-ddbab905708a"}
{"timestamp": "2025-05-28T13:45:31.250650+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 14.34}, "user_id": null, "request_id": "07bf58f1-d87b-473f-a47f-6550b6ff4b2e"}
{"timestamp": "2025-05-28T13:45:32.567041+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 7.54}, "user_id": null, "request_id": "a1b1e002-0d50-4394-8d37-6af0fd2fe754"}
{"timestamp": "2025-05-28T13:45:32.589982+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.73}, "user_id": null, "request_id": "12fe3694-67f0-4121-b8a7-b8dc34af87a5"}
{"timestamp": "2025-05-28T13:45:36.025636+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (0.572s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 571.76}, "user_id": null, "request_id": "4cd76e38-3464-40a6-b511-0b277e8e87c6"}
{"timestamp": "2025-05-28T13:47:05.305136+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (88.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 88005.0}, "user_id": null, "request_id": "fc8e7f05-548a-480f-87fb-f3209c72afe2"}
{"timestamp": "2025-05-28T13:47:07.106095+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.784s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1783.83}, "user_id": null, "request_id": "4a5a6540-190f-4f16-a42b-164327fd2f60"}
{"timestamp": "2025-05-28T13:48:47.870793+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/1/sync - 200 (86.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/1/sync", "status_code": 200, "duration_ms": 86010.64}, "user_id": null, "request_id": "47459bc8-d830-4ffc-96b9-2beee5e47607"}
{"timestamp": "2025-05-28T16:01:28.210804+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 7.09}, "user_id": null, "request_id": "3a5681b1-77ea-4735-8682-efa3b67a7dfc"}
{"timestamp": "2025-05-28T16:01:28.233963+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 5.51}, "user_id": null, "request_id": "fa84c872-4874-44a1-b1b3-c1c428dc3eca"}
{"timestamp": "2025-05-28T16:01:28.244042+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 7.51}, "user_id": null, "request_id": "2eae588c-e173-4ed6-b5fd-adeabf16adac"}
{"timestamp": "2025-05-28T16:01:29.634423+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 6.95}, "user_id": null, "request_id": "ab73d3eb-7d2a-43c2-b6ea-a5289b1a4be5"}
{"timestamp": "2025-05-28T16:01:29.655402+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 3.42}, "user_id": null, "request_id": "719e5e91-e8cd-4565-87ab-74293a1c40e0"}
{"timestamp": "2025-05-28T16:01:29.655990+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 3.5}, "user_id": null, "request_id": "88a5016f-59ca-44cf-be8b-0d5ac8a1d85d"}
{"timestamp": "2025-05-28T16:01:29.656523+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 3.37}, "user_id": null, "request_id": "699542c3-f026-4ca4-8544-2ef6d225d04b"}
{"timestamp": "2025-05-28T16:01:29.663083+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.84}, "user_id": null, "request_id": "5191c519-8419-4903-8884-34bbb6b94916"}
{"timestamp": "2025-05-28T16:01:33.847477+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.254s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 253.89}, "user_id": null, "request_id": "def861bb-634e-429f-bd14-8d40a8cef57d"}
{"timestamp": "2025-05-28T16:01:34.865274+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 3.73}, "user_id": null, "request_id": "0be0d4d0-676c-4851-9a29-b5be1bb66aba"}
{"timestamp": "2025-05-28T16:01:34.904238+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.020s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 20.21}, "user_id": null, "request_id": "3f3819bb-b490-4a4a-ad3c-6d21a23a3469"}
{"timestamp": "2025-05-28T16:01:34.908854+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.024s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 23.98}, "user_id": null, "request_id": "1c3a8347-5340-4b90-9b2c-8eaa02aa3d72"}
{"timestamp": "2025-05-28T16:01:34.909403+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.021s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 20.78}, "user_id": null, "request_id": "66f21922-c01b-47e4-b8a4-d4d5cb5e936f"}
{"timestamp": "2025-05-28T16:01:35.893996+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.92}, "user_id": null, "request_id": "24548e55-d1a1-4df3-82e6-35713a6894d8"}
{"timestamp": "2025-05-28T16:01:35.908365+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.89}, "user_id": null, "request_id": "1ed6baf3-313b-4788-8b64-925ac9ad7f46"}
{"timestamp": "2025-05-28T16:01:40.539499+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (0.679s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 678.84}, "user_id": null, "request_id": "d0fa6077-e5ec-4d97-9883-a96e6a860c6a"}
{"timestamp": "2025-05-28T16:03:12.362643+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (89.988s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 89987.8}, "user_id": null, "request_id": "cb3572ac-d731-4e13-b0d3-80c400693ebc"}
{"timestamp": "2025-05-28T16:03:15.670506+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.290s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3290.11}, "user_id": null, "request_id": "05a4a2ba-bf9f-4557-951a-eb2526c49cfa"}
{"timestamp": "2025-05-28T16:04:48.897687+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/2/sync - 200 (73.327s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/2/sync", "status_code": 200, "duration_ms": 73326.92}, "user_id": null, "request_id": "b40044a5-ae26-4640-aa2e-d84c654da742"}
{"timestamp": "2025-05-28T16:04:50.989586+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.083s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2083.44}, "user_id": null, "request_id": "edc0873b-961e-4bb3-99ae-f690c377414a"}
{"timestamp": "2025-05-28T16:14:14.162322+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/2 - 200 (0.443s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/2", "status_code": 200, "duration_ms": 443.08}, "user_id": null, "request_id": "b629c68c-8232-492e-b1e1-9b0e40e91c8a"}
{"timestamp": "2025-05-28T16:14:14.180740+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 11.7}, "user_id": null, "request_id": "9f45edac-67cf-4815-8a86-81c6fcf82d70"}
{"timestamp": "2025-05-28T16:14:15.681408+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 7.39}, "user_id": null, "request_id": "98089d35-644f-4808-b32f-7578086207b4"}
{"timestamp": "2025-05-28T16:14:15.712272+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 14.38}, "user_id": null, "request_id": "5f36a09c-d983-4230-a871-087c97e2a377"}
{"timestamp": "2025-05-28T16:14:15.712761+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 14.49}, "user_id": null, "request_id": "5526d719-c2df-4f72-866f-dbb53a0d140c"}
{"timestamp": "2025-05-28T16:14:15.713298+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 14.96}, "user_id": null, "request_id": "fb188954-6d04-4ec9-80b3-618c2267eab0"}
{"timestamp": "2025-05-28T16:14:18.119204+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.62}, "user_id": null, "request_id": "ce8235c3-8c70-4095-9510-937d8c86df10"}
{"timestamp": "2025-05-28T16:14:21.366089+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.240s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 240.11}, "user_id": null, "request_id": "10f27178-6514-4c94-ae57-12864140e3d6"}
{"timestamp": "2025-05-28T16:14:22.381360+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.06}, "user_id": null, "request_id": "06f59082-a3a3-480c-a33a-5dbedac37b0c"}
{"timestamp": "2025-05-28T16:14:22.414369+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 14.63}, "user_id": null, "request_id": "8704aa39-e4a1-4f3a-b74c-d77e3ebc57df"}
{"timestamp": "2025-05-28T16:14:22.414653+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.014s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 13.91}, "user_id": null, "request_id": "c085e9ae-9c2b-45f1-91b3-a4c007c0d186"}
{"timestamp": "2025-05-28T16:14:22.415241+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 13.17}, "user_id": null, "request_id": "7b9b48dd-5b66-4363-8c33-a1841579e173"}
{"timestamp": "2025-05-28T16:14:25.282051+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.38}, "user_id": null, "request_id": "a0956e14-50da-4e38-ae29-a2b774619220"}
{"timestamp": "2025-05-28T16:14:27.559980+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.219s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 219.12}, "user_id": null, "request_id": "240bfbfb-2036-4f1a-a84e-ce406fa8d950"}
{"timestamp": "2025-05-28T16:14:28.570700+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.53}, "user_id": null, "request_id": "565a40a9-fb1c-47c6-96ed-8108b2a4a60f"}
{"timestamp": "2025-05-28T16:14:28.590261+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7.49}, "user_id": null, "request_id": "c7fb7cbe-3ace-41c9-8073-647671ee8f91"}
{"timestamp": "2025-05-28T16:14:28.599765+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.010s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 10.25}, "user_id": null, "request_id": "60e3f61b-91e2-4c7c-b498-b811c7cc7e48"}
{"timestamp": "2025-05-28T16:14:28.600469+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 10.59}, "user_id": null, "request_id": "aae8470e-e553-4c6f-bddd-1ae50ea4a905"}
{"timestamp": "2025-05-28T16:15:15.675560+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 9.27}, "user_id": null, "request_id": "f303ea36-5595-4b0a-ac8e-3297162179c1"}
{"timestamp": "2025-05-28T16:15:15.708113+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.010s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 10.26}, "user_id": null, "request_id": "825cb013-c57d-4cca-adac-d73a0c60b83f"}
{"timestamp": "2025-05-28T16:15:20.379298+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/test-connection - 200 (0.589s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/test-connection", "status_code": 200, "duration_ms": 588.74}, "user_id": null, "request_id": "9597e424-7b2e-4795-94c2-61b6fd7293cb"}
{"timestamp": "2025-05-28T16:16:48.844551+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/manual - 201 (87.577s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/manual", "status_code": 201, "duration_ms": 87577.01}, "user_id": null, "request_id": "324fba61-ee24-401e-bb1b-0f48f22a892e"}
{"timestamp": "2025-05-28T16:16:54.343945+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (5.492s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5491.72}, "user_id": null, "request_id": "47575b22-f85f-479f-9766-e72a8bfa868a"}
{"timestamp": "2025-05-28T16:17:43.815979+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/3/sync - 200 (26.677s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/3/sync", "status_code": 200, "duration_ms": 26677.18}, "user_id": null, "request_id": "c205cf18-668e-49d3-9573-e5b3ec221ebd"}
{"timestamp": "2025-05-28T16:17:45.939054+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.121s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2120.95}, "user_id": null, "request_id": "dfe3e3a6-aa0f-4e02-bc96-695ab5cca731"}
{"timestamp": "2025-05-28T16:40:40.613890+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/3/sync - 200 (25.779s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/3/sync", "status_code": 200, "duration_ms": 25779.45}, "user_id": null, "request_id": "f9834c79-2d1e-4682-8929-dacb5b424eca"}
{"timestamp": "2025-05-28T16:40:42.696985+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.077s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2076.93}, "user_id": null, "request_id": "ec0f6724-a3cc-4961-b4d8-37356f2075a2"}
{"timestamp": "2025-05-28T16:40:49.639710+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 8.48}, "user_id": null, "request_id": "55e54811-72fe-4ee1-a27b-37374766e140"}
{"timestamp": "2025-05-28T16:40:51.724020+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.067s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2067.15}, "user_id": null, "request_id": "8e81533b-6ebb-43f2-a4c6-6f5c2e073cc4"}
{"timestamp": "2025-05-28T16:40:51.724653+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (2.067s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 2067.23}, "user_id": null, "request_id": "ede2dcf9-baac-487f-96c0-7ee6f316db68"}
{"timestamp": "2025-05-28T16:40:51.725139+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.067s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2067.38}, "user_id": null, "request_id": "67edb979-a988-43bb-b003-feb6dd7b8453"}
{"timestamp": "2025-05-28T16:46:06.463744+00:00", "level": "INFO", "logger": "api", "message": "GET /profile - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/profile", "status_code": 200, "duration_ms": 6.81}, "user_id": null, "request_id": "253c1c18-92ba-49e9-b235-659cd4ddca3d"}
{"timestamp": "2025-05-28T16:46:06.482922+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/style.css - 404 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/style.css", "status_code": 404, "duration_ms": 1.54}, "user_id": null, "request_id": "dbdbb07e-a12e-4fb4-9441-49061b6eb67d"}
{"timestamp": "2025-05-28T16:46:06.504655+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 7.75}, "user_id": null, "request_id": "97cc8f16-635a-4c25-9915-fd61152f8349"}
{"timestamp": "2025-05-28T16:46:06.515750+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 401 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 401, "duration_ms": 1.27}, "user_id": null, "request_id": "a925b6fd-7d1e-4775-8987-31eed851b95f"}
{"timestamp": "2025-05-28T16:46:07.720745+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 2.83}, "user_id": null, "request_id": "62930412-93f1-472d-b883-0e74262f5bb3"}
{"timestamp": "2025-05-28T16:46:07.737165+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 3.64}, "user_id": null, "request_id": "9d0ba530-70cb-48b3-9a5d-483b724971ba"}
{"timestamp": "2025-05-28T16:46:07.738014+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 4.0}, "user_id": null, "request_id": "0adeff34-bbc6-4c71-8afd-597b1e464205"}
{"timestamp": "2025-05-28T16:46:07.744663+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.43}, "user_id": null, "request_id": "18372a44-bdec-4d7a-8dce-8c4e553329fc"}
{"timestamp": "2025-05-28T16:46:10.705335+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.236s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 235.99}, "user_id": null, "request_id": "05d0a0f8-8316-4e65-8c3d-c16bb39d3af5"}
{"timestamp": "2025-05-28T16:46:11.722642+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 7.08}, "user_id": null, "request_id": "1fa4e752-2c3f-4580-a049-e10569168974"}
{"timestamp": "2025-05-28T16:46:11.753988+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.021s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 20.55}, "user_id": null, "request_id": "b6accca9-1225-4377-b701-e387219f14a6"}
{"timestamp": "2025-05-28T16:46:11.754477+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.021s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 21.29}, "user_id": null, "request_id": "9aa9eefd-2882-48d8-a236-1331a6927031"}
{"timestamp": "2025-05-28T17:58:51.474984+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 4.63}, "user_id": null, "request_id": "3152c994-ba44-4151-8cb1-47f442cf0222"}
{"timestamp": "2025-05-28T17:58:54.491876+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 5.46}, "user_id": null, "request_id": "ed625b24-8368-42bc-8239-72652f982e6b"}
{"timestamp": "2025-05-28T17:58:54.529829+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 11.82}, "user_id": null, "request_id": "76e57781-1a68-41c7-b6b8-d6c7eb5276a6"}
{"timestamp": "2025-05-28T17:58:54.530207+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 12.41}, "user_id": null, "request_id": "535945a3-c12a-474d-882e-f15445fae2d9"}
{"timestamp": "2025-05-28T17:58:54.536587+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.91}, "user_id": null, "request_id": "ce9000cd-0189-4867-86b2-1bdb790c3352"}
{"timestamp": "2025-05-28T17:58:56.016098+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.315s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 314.85}, "user_id": null, "request_id": "c408c55d-9eb0-4a1b-b587-7db7bed373c5"}
{"timestamp": "2025-05-28T17:58:57.032430+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.1}, "user_id": null, "request_id": "1f2e69b0-93a0-4b65-a0ff-beecf2ae4e3b"}
{"timestamp": "2025-05-28T17:58:57.076771+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.025s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 25.04}, "user_id": null, "request_id": "7b489865-ee2b-4d4b-a328-8a00136eb111"}
{"timestamp": "2025-05-28T17:58:57.077335+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.025s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 25.02}, "user_id": null, "request_id": "b4ed5f8f-86c9-4a62-9e7b-fefce26055b1"}
{"timestamp": "2025-05-28T17:58:58.411617+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.09}, "user_id": null, "request_id": "efb20962-13ba-4d92-9b29-daf8afc139ac"}
{"timestamp": "2025-05-28T17:58:58.440790+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7.0}, "user_id": null, "request_id": "eeb7ba3f-bd5e-4985-b7cc-df794506b5f5"}
{"timestamp": "2025-05-28T18:00:42.583981+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 2.87}, "user_id": null, "request_id": "6487c727-1659-40bf-b19b-d20a348f7024"}
{"timestamp": "2025-05-28T18:00:42.605700+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7.72}, "user_id": null, "request_id": "020c5728-ce5c-468e-abe8-733bb7e59fae"}
{"timestamp": "2025-05-28T18:03:49.747730+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.45}, "user_id": null, "request_id": "df79d99f-a965-4bfe-aa8e-e276e14cdad9"}
{"timestamp": "2025-05-28T18:03:49.777026+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.020s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 20.18}, "user_id": null, "request_id": "8a0a3afa-ab9c-41ab-8558-b28eaa8b1389"}
{"timestamp": "2025-05-28T18:06:38.512741+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 6.92}, "user_id": null, "request_id": "e9f9cc22-e772-437a-84f7-5ccffa9e6f27"}
{"timestamp": "2025-05-28T18:06:38.549514+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.023s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 23.37}, "user_id": null, "request_id": "0ed7d07c-66e4-4217-839c-749cd7c8bc59"}
{"timestamp": "2025-05-28T18:07:10.654391+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/test - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/test", "status_code": 200, "duration_ms": 3.26}, "user_id": null, "request_id": "c60ab919-6902-459b-9848-0212655d3240"}
{"timestamp": "2025-05-28T18:07:10.736790+00:00", "level": "INFO", "logger": "api", "message": "GET /favicon.ico - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/favicon.ico", "status_code": 404, "duration_ms": 1.39}, "user_id": null, "request_id": "8b85a555-685b-4322-849f-b503ecb180ae"}
{"timestamp": "2025-05-28T18:07:24.509938+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/test-page - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/test-page", "status_code": 200, "duration_ms": 1.06}, "user_id": null, "request_id": "27a34848-56fa-4eb9-afe4-178eb054eb87"}
{"timestamp": "2025-05-28T18:07:40.836247+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.98}, "user_id": null, "request_id": "1a9456d4-0052-4ddb-a58f-8c04a113c48f"}
{"timestamp": "2025-05-28T18:07:40.919434+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/styles.css - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/styles.css", "status_code": 200, "duration_ms": 10.94}, "user_id": null, "request_id": "f957a044-a873-4dd9-8ba4-bdef1fa907f9"}
{"timestamp": "2025-05-28T18:07:40.922163+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/auth.js - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/auth.js", "status_code": 200, "duration_ms": 2.95}, "user_id": null, "request_id": "6206d242-ce57-4f6c-a984-251fee3652e2"}
{"timestamp": "2025-05-28T18:07:40.922391+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 200, "duration_ms": 2.37}, "user_id": null, "request_id": "57a071c5-cfca-445e-834b-fe57a609071d"}
{"timestamp": "2025-05-28T18:07:41.018970+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 3.8}, "user_id": null, "request_id": "ad365e06-be02-4c83-b5ac-5881daf47ab4"}
{"timestamp": "2025-05-28T18:07:47.098032+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 5.0}, "user_id": null, "request_id": "424ea02e-9a3e-49b1-816a-12855b6d9ad6"}
{"timestamp": "2025-05-28T18:07:48.227802+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.16}, "user_id": null, "request_id": "67d5f288-51c9-4db0-8112-9872d841e65d"}
{"timestamp": "2025-05-28T18:07:59.490000+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 6.3}, "user_id": null, "request_id": "32cb524e-c0c6-4f6f-bd21-28b2c8f2e4da"}
{"timestamp": "2025-05-28T18:07:59.519707+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 12.5}, "user_id": null, "request_id": "9af64e1b-c601-4da2-b4d3-7d1f9fa28718"}
{"timestamp": "2025-05-28T18:07:59.520548+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 12.7}, "user_id": null, "request_id": "9dd784af-ddbe-4639-afab-5d51f35c84a6"}
{"timestamp": "2025-05-28T18:08:01.687785+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.42}, "user_id": null, "request_id": "94063e92-332a-42a8-aeed-5ca37a5d565d"}
{"timestamp": "2025-05-28T18:08:15.880598+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.237s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 237.15}, "user_id": null, "request_id": "c37ab1f6-0fd5-4ae7-b429-e410546139a1"}
{"timestamp": "2025-05-28T18:08:16.949931+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.46}, "user_id": null, "request_id": "a90d67f0-451b-4a30-b398-5869d131e50b"}
{"timestamp": "2025-05-28T18:08:17.040438+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 12.89}, "user_id": null, "request_id": "0662922b-ee89-4bb4-9088-47798d1f5e42"}
{"timestamp": "2025-05-28T18:08:17.046801+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 7.15}, "user_id": null, "request_id": "5122f7bc-53ba-4c17-a211-2e9c7036ffa9"}
{"timestamp": "2025-05-28T18:08:21.178145+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.2}, "user_id": null, "request_id": "33364c56-e73a-4af3-ad65-0ec3591e22b9"}
{"timestamp": "2025-05-28T18:08:21.247988+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 1.31}, "user_id": null, "request_id": "e8cb65d6-b568-4e75-b3e3-e18265870901"}
{"timestamp": "2025-05-28T18:08:21.311994+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.33}, "user_id": null, "request_id": "a342e8b5-0922-4fee-8aa1-4cc7481a4574"}
{"timestamp": "2025-05-28T18:08:40.536383+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 307 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 307, "duration_ms": 6.14}, "user_id": null, "request_id": "a0ebcfb7-3560-42c4-abbe-7d773f6c971d"}
{"timestamp": "2025-05-28T18:09:08.892276+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/test - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/test", "status_code": 200, "duration_ms": 1.72}, "user_id": null, "request_id": "375f949f-0a50-45f6-99c9-c27cb0d1cae8"}
{"timestamp": "2025-05-28T18:09:25.401964+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 403 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 403, "duration_ms": 1.15}, "user_id": null, "request_id": "b9acf149-5096-46d1-b62a-2ee987e19120"}
{"timestamp": "2025-05-28T18:09:43.541512+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.222s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 221.71}, "user_id": null, "request_id": "830a27bd-522e-430a-9e28-29f238e9305e"}
{"timestamp": "2025-05-28T18:09:44.563626+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.97}, "user_id": null, "request_id": "68fb946c-3abd-42f4-8c5f-266743b426ed"}
{"timestamp": "2025-05-28T18:09:44.586121+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6.32}, "user_id": null, "request_id": "209711de-2158-4e0e-8781-2781e8c87ec4"}
{"timestamp": "2025-05-28T18:09:44.586426+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 6.44}, "user_id": null, "request_id": "f0d677f1-ef10-48a9-b5da-50c477085330"}
{"timestamp": "2025-05-28T18:09:46.311605+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/test - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/test", "status_code": 200, "duration_ms": 1.2}, "user_id": null, "request_id": "6626da25-ff9f-4845-8967-f97a8a2b39dd"}
{"timestamp": "2025-05-28T18:10:05.243255+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/test-page - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/test-page", "status_code": 200, "duration_ms": 2.6}, "user_id": null, "request_id": "6302a27f-f2f8-4c0e-a407-d7a08ede89a6"}
{"timestamp": "2025-05-28T18:10:16.360069+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.97}, "user_id": null, "request_id": "63d79733-6787-46b4-8544-6cebea87de46"}
{"timestamp": "2025-05-28T18:10:16.437120+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 2.25}, "user_id": null, "request_id": "88e1a26f-39f2-458d-917e-00f2e9c2dc74"}
{"timestamp": "2025-05-28T18:10:16.507248+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 12.18}, "user_id": null, "request_id": "3687b47e-e8ae-4c16-b2bd-2d64659f7fe8"}
{"timestamp": "2025-05-28T18:11:05.717614+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 307 (0.010s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 307, "duration_ms": 9.68}, "user_id": null, "request_id": "244e07a8-8b0c-4a15-9e64-a17956084566"}
{"timestamp": "2025-05-28T18:17:09.810214+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 6.61}, "user_id": null, "request_id": "7018c2a2-0c16-45a0-b1bc-01c6ac1e299d"}
{"timestamp": "2025-05-28T18:17:09.854785+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.027s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 27.08}, "user_id": null, "request_id": "40070187-2bf0-4df8-9db5-e469b74cfa4e"}
{"timestamp": "2025-05-28T18:17:09.855207+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.027s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 26.77}, "user_id": null, "request_id": "123fb6f4-a5b7-45a4-acf0-c23b46293995"}
{"timestamp": "2025-05-28T18:17:12.963752+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 6.28}, "user_id": null, "request_id": "4d73d0c0-e989-4d98-8d04-61b9cc0fefaa"}
{"timestamp": "2025-05-28T18:17:12.983489+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.59}, "user_id": null, "request_id": "2fe5e7e5-9521-4cd1-b903-6889ea13d1d6"}
{"timestamp": "2025-05-29T04:24:20.731712+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.76}, "user_id": null, "request_id": "d2231b6b-1e22-4b13-b131-1adf4b2cada2"}
{"timestamp": "2025-05-29T04:24:20.750182+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/auth.js - 304 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/auth.js", "status_code": 304, "duration_ms": 5.25}, "user_id": null, "request_id": "9c84653c-d92c-41b1-a2c5-54bb1b29448b"}
{"timestamp": "2025-05-29T04:24:20.750390+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/styles.css - 304 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/styles.css", "status_code": 304, "duration_ms": 5.98}, "user_id": null, "request_id": "c780f352-c256-498a-b728-5e717912f542"}
{"timestamp": "2025-05-29T04:24:20.750494+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 200, "duration_ms": 5.1}, "user_id": null, "request_id": "6eb4bdee-e7b2-42f1-87b2-dd5305433ddd"}
{"timestamp": "2025-05-29T04:24:20.757370+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 4.48}, "user_id": null, "request_id": "ea04df53-87e7-426d-8858-069af79aeb3d"}
{"timestamp": "2025-05-29T04:24:23.791793+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 6.88}, "user_id": null, "request_id": "b9393eb6-c195-4bdd-9a8c-62a3303ef22e"}
{"timestamp": "2025-05-29T04:24:23.804447+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 2.69}, "user_id": null, "request_id": "46f0c26d-52da-42c0-bbe3-c2e522ce738c"}
{"timestamp": "2025-05-29T04:24:23.808584+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 5.81}, "user_id": null, "request_id": "f13899d3-8644-4245-8dbe-90fa3e8ed4c2"}
{"timestamp": "2025-05-29T04:24:23.815757+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.34}, "user_id": null, "request_id": "aa3b3684-ced6-4b65-8109-54477cab136a"}
{"timestamp": "2025-05-29T04:24:25.132742+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.238s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 237.57}, "user_id": null, "request_id": "d8254f42-fa51-42f5-a387-67b6adf982f3"}
{"timestamp": "2025-05-29T04:24:26.146611+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.56}, "user_id": null, "request_id": "782be27e-bd23-4ba0-906f-caf22bce977f"}
{"timestamp": "2025-05-29T04:24:26.184129+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 17.1}, "user_id": null, "request_id": "37ecee4a-1687-47c1-a384-9ecac1f2648f"}
{"timestamp": "2025-05-29T04:24:26.184980+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.017s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 16.85}, "user_id": null, "request_id": "a8ba2063-7c6b-40c8-bc72-25c197a2cd45"}
{"timestamp": "2025-05-29T04:24:27.620389+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.26}, "user_id": null, "request_id": "da2d09fe-7e77-4201-ba6a-a92c87cced0f"}
{"timestamp": "2025-05-29T04:24:30.262819+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.214s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 214.0}, "user_id": null, "request_id": "89e85d14-ff14-4ae5-b2b8-d7a5f1fb56ff"}
{"timestamp": "2025-05-29T04:24:31.280793+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.52}, "user_id": null, "request_id": "e7a1537b-4e8a-4298-9c1a-e605549eccd1"}
{"timestamp": "2025-05-29T04:24:33.400822+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.102s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2101.75}, "user_id": null, "request_id": "8d083cee-fd0f-470d-9cc1-5350e330cd56"}
{"timestamp": "2025-05-29T04:24:33.401718+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.102s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2101.95}, "user_id": null, "request_id": "7aae1bdc-ad4c-48d6-b31a-3d1df93a7f1b"}
{"timestamp": "2025-05-29T05:47:19.067133+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 4.52}, "user_id": null, "request_id": "361b2fac-a3f5-432a-bbd0-31900991c68a"}
{"timestamp": "2025-05-29T05:47:20.962076+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 7.32}, "user_id": null, "request_id": "82c848b9-6b97-4965-9e8d-ee816d9348fb"}
{"timestamp": "2025-05-29T05:47:20.990650+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 9.29}, "user_id": null, "request_id": "7391618c-7fe0-411d-a908-5e6463e52fe9"}
{"timestamp": "2025-05-29T05:47:20.991401+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 8.25}, "user_id": null, "request_id": "e3f6e824-a3ac-4413-8e0e-d10164bd63c9"}
{"timestamp": "2025-05-29T05:47:20.999838+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.45}, "user_id": null, "request_id": "adb35b74-bb80-485f-91fe-5ebc4954f58d"}
{"timestamp": "2025-05-29T05:47:22.546900+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.303s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 303.48}, "user_id": null, "request_id": "74dec1f4-adb6-4fba-9f5d-07dacd69d1f6"}
{"timestamp": "2025-05-29T05:47:23.562460+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.18}, "user_id": null, "request_id": "f47a19fe-7d35-4701-b9ce-7e776163a33b"}
{"timestamp": "2025-05-29T05:47:26.763260+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.180s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3179.88}, "user_id": null, "request_id": "226287c6-0e5d-4ae7-bdc0-1cab87611837"}
{"timestamp": "2025-05-29T05:47:26.764712+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (3.181s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 3180.87}, "user_id": null, "request_id": "234f557f-3b52-4880-ad5e-d5d997dc5259"}
{"timestamp": "2025-05-29T05:47:44.361112+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.01}, "user_id": null, "request_id": "5f3f9b0b-cc69-4e42-8780-ce962f196aa6"}
{"timestamp": "2025-05-29T05:47:44.376856+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 7.19}, "user_id": null, "request_id": "0972613a-7afe-42e2-8bfe-ee63413e10d3"}
{"timestamp": "2025-05-29T05:47:48.479420+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (4.100s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4099.83}, "user_id": null, "request_id": "78519985-c784-479b-b478-ea250b69506b"}
{"timestamp": "2025-05-29T06:11:55.529435+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 2.31}, "user_id": null, "request_id": "f13c8618-b88d-40ab-8dfa-dcfc03391764"}
{"timestamp": "2025-05-29T06:12:02.813161+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (7.265s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7264.99}, "user_id": null, "request_id": "0b3ff5eb-ecc9-48d2-b219-a65e3441d852"}
{"timestamp": "2025-05-29T06:12:09.146727+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 5.8}, "user_id": null, "request_id": "93e3d6d5-df81-4ddc-ae22-a0eb27562200"}
{"timestamp": "2025-05-29T06:12:15.018583+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (5.860s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5860.42}, "user_id": null, "request_id": "f3a8cbfd-0e34-449e-81f0-958ca99f3530"}
{"timestamp": "2025-05-29T06:12:15.019105+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (5.860s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 5860.47}, "user_id": null, "request_id": "ca64158e-b7b5-4e25-ac3f-bb8a1c2d1642"}
{"timestamp": "2025-05-29T06:12:16.471066+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.65}, "user_id": null, "request_id": "80b6c2e3-b0e7-4a35-a16a-df94344755aa"}
{"timestamp": "2025-05-29T06:12:25.067673+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (8.583s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8582.83}, "user_id": null, "request_id": "f6588c2b-c796-4591-b553-209ebe8fb7dd"}
{"timestamp": "2025-05-29T06:14:30.009737+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 3.85}, "user_id": null, "request_id": "7fbd3cfa-4e13-4ca3-ba33-58c4da83f23f"}
{"timestamp": "2025-05-29T06:14:34.563187+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 5.07}, "user_id": null, "request_id": "38f15978-9d48-4d30-bc83-7fd124cca06a"}
{"timestamp": "2025-05-29T06:14:35.591559+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.224s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 224.15}, "user_id": null, "request_id": "848796de-d906-43f4-a9f5-5268378eee26"}
{"timestamp": "2025-05-29T06:14:36.609407+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.83}, "user_id": null, "request_id": "c63e4c88-fbc5-4af9-b0e9-9c3aba1cab74"}
{"timestamp": "2025-05-29T06:14:47.160524+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (10.537s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 10536.65}, "user_id": null, "request_id": "6d7a4c63-22cf-40a1-95e1-0b0f46d8fa1c"}
{"timestamp": "2025-05-29T06:14:47.161514+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (10.537s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 10537.27}, "user_id": null, "request_id": "dd817492-5a87-4940-bf7a-446abb3f15a2"}
{"timestamp": "2025-05-29T06:14:52.561927+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.55}, "user_id": null, "request_id": "2a6d35f1-4a7a-4a51-8a16-5ddb867c5850"}
{"timestamp": "2025-05-29T06:14:55.898258+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.324s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3323.55}, "user_id": null, "request_id": "d9a19638-c168-4d4d-9c9c-95fa44408b64"}
{"timestamp": "2025-05-29T07:40:33.310835+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 2.77}, "user_id": null, "request_id": "433d62b0-809d-4764-9cbf-ca8a899980e5"}
{"timestamp": "2025-05-29T07:40:33.325268+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 3.94}, "user_id": null, "request_id": "ab91f796-c2b8-423b-b52e-ca0736e2d8cf"}
{"timestamp": "2025-05-29T07:40:33.331922+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 4.52}, "user_id": null, "request_id": "c697790c-b40a-4a12-bea8-35cb2dc3f7d3"}
{"timestamp": "2025-05-29T07:40:34.649928+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 6.28}, "user_id": null, "request_id": "0b2031f2-cd57-4516-85be-1b0e11e9e6d7"}
{"timestamp": "2025-05-29T07:40:34.660886+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 1.6}, "user_id": null, "request_id": "110884fc-5b36-4102-ac9e-ae8e9537b5d7"}
{"timestamp": "2025-05-29T07:40:34.665572+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 2.5}, "user_id": null, "request_id": "6a4cd915-cc10-4350-a938-7ed00fbddd90"}
{"timestamp": "2025-05-29T07:40:34.672018+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 2.27}, "user_id": null, "request_id": "aefa07c6-a81c-4203-bbdf-8a141650c51a"}
{"timestamp": "2025-05-29T07:40:36.275728+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.232s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 232.49}, "user_id": null, "request_id": "49c607db-1191-4f66-b665-06d614cf08db"}
{"timestamp": "2025-05-29T07:40:37.291260+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.12}, "user_id": null, "request_id": "999321c9-062e-4252-8ce0-629529fbe47d"}
{"timestamp": "2025-05-29T07:40:42.294030+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (4.984s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4983.59}, "user_id": null, "request_id": "4fbd74e2-c86e-4562-8729-d29a2080efef"}
{"timestamp": "2025-05-29T07:40:42.294737+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (4.984s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 4984.21}, "user_id": null, "request_id": "137b2e60-120f-4c97-b0f8-01eabd5215f6"}
{"timestamp": "2025-05-29T07:40:43.510369+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.82}, "user_id": null, "request_id": "253f91c2-8302-47ae-bb95-adc84662a0fe"}
{"timestamp": "2025-05-29T07:40:54.727300+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (11.204s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 11203.61}, "user_id": null, "request_id": "242a6c87-eaaf-4bf5-861e-8129300c830d"}
{"timestamp": "2025-05-29T07:40:54.729046+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 2.42}, "user_id": null, "request_id": "6ccc8f58-f85a-4694-a951-4a961b30fe3d"}
{"timestamp": "2025-05-29T07:40:58.525615+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.782s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3781.81}, "user_id": null, "request_id": "1fac6261-5d3a-4352-81f6-95ccab7b6a58"}
{"timestamp": "2025-05-29T08:05:20.139630+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 2.21}, "user_id": null, "request_id": "0a3f754c-e99a-4b8a-8e50-0990a44b5027"}
{"timestamp": "2025-05-29T08:05:27.438190+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (7.284s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7284.23}, "user_id": null, "request_id": "6ee87a0c-208d-4726-85be-5efab7da4c78"}
{"timestamp": "2025-05-29T08:05:27.439855+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (7.284s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 7284.27}, "user_id": null, "request_id": "ed8fbbdb-2ff3-44f4-a188-33ca8470f168"}
{"timestamp": "2025-05-29T08:05:27.440313+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 2.34}, "user_id": null, "request_id": "bf079259-c542-4c8f-b1c5-adcb458b3cf6"}
{"timestamp": "2025-05-29T08:05:31.753046+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (4.283s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4283.04}, "user_id": null, "request_id": "72204f90-16b4-44bd-b363-73c59d13674a"}
{"timestamp": "2025-05-29T08:38:02.119786+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 3.51}, "user_id": null, "request_id": "787098fa-5800-4ba4-a9a4-4a0f97ff8934"}
{"timestamp": "2025-05-29T08:38:04.024979+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.74}, "user_id": null, "request_id": "503fa565-5ee3-4e2b-8416-0b58da640245"}
{"timestamp": "2025-05-29T08:38:04.079033+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 29.04}, "user_id": null, "request_id": "9eddb5e8-ffb1-4c41-b401-af95c8f6360a"}
{"timestamp": "2025-05-29T08:38:04.079909+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 29.29}, "user_id": null, "request_id": "a0ad2c17-59b0-464d-a043-3d4812013255"}
{"timestamp": "2025-05-29T08:38:04.085231+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.76}, "user_id": null, "request_id": "a2478e38-92fe-486d-ae15-730de72f6353"}
{"timestamp": "2025-05-29T08:38:07.734452+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.232s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 232.26}, "user_id": null, "request_id": "2d5254d8-f3a3-4f97-954e-0924f450d392"}
{"timestamp": "2025-05-29T08:38:08.749713+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.06}, "user_id": null, "request_id": "53992afe-e9de-4c75-a0d8-3267d4efe58d"}
{"timestamp": "2025-05-29T08:38:10.854657+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.084s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2084.47}, "user_id": null, "request_id": "95aeceb0-208c-475a-a145-e779d169a01c"}
{"timestamp": "2025-05-29T08:38:10.855453+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.086s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2085.96}, "user_id": null, "request_id": "536c7c26-116d-4d67-b2bf-4d59b8da243a"}
{"timestamp": "2025-05-29T08:38:11.528689+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.99}, "user_id": null, "request_id": "4744a247-de46-4534-9dca-d0f3612999e8"}
{"timestamp": "2025-05-29T08:38:13.656465+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.111s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2110.55}, "user_id": null, "request_id": "48c39861-563c-4edb-8f3b-1894e58ac78f"}
{"timestamp": "2025-05-29T09:51:43.398105+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.77}, "user_id": null, "request_id": "2fdcdf3e-af5b-4999-a812-311ebce74035"}
{"timestamp": "2025-05-29T09:51:43.412729+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 200, "duration_ms": 4.91}, "user_id": null, "request_id": "374442ce-408a-4b4f-9fc8-89bec8460f7a"}
{"timestamp": "2025-05-29T09:51:43.421967+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 6.11}, "user_id": null, "request_id": "16cd0dff-9dde-47ba-bf91-b4a1237c1a30"}
{"timestamp": "2025-05-29T09:51:55.167035+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 307 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 307, "duration_ms": 4.38}, "user_id": null, "request_id": "e4a1d43a-7f51-497f-a6a4-6ea5651f7e5a"}
{"timestamp": "2025-05-29T09:52:24.333606+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/callback - 403 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/callback", "status_code": 403, "duration_ms": 0.56}, "user_id": null, "request_id": "fe5e1c5e-aef6-47ea-9411-c9037fb045d3"}
{"timestamp": "2025-05-29T09:52:24.392572+00:00", "level": "INFO", "logger": "api", "message": "GET /favicon.ico - 404 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/favicon.ico", "status_code": 404, "duration_ms": 2.16}, "user_id": null, "request_id": "883346b9-278a-4fd8-a01a-682124fafcf7"}
{"timestamp": "2025-05-29T10:00:11.588979+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.97}, "user_id": null, "request_id": "7d26a096-ee0c-48c2-afd7-1e8ad52875df"}
{"timestamp": "2025-05-29T10:00:11.607364+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 4.91}, "user_id": null, "request_id": "07e7bc98-2336-48fe-b9e1-93a8cd2dbb6e"}
{"timestamp": "2025-05-29T10:00:11.610449+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 7.38}, "user_id": null, "request_id": "feeef709-f3c6-4b98-96b3-b27134f95851"}
{"timestamp": "2025-05-29T10:00:11.616496+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.94}, "user_id": null, "request_id": "7c471ab9-93d8-4da6-a324-1868e89fab3b"}
{"timestamp": "2025-05-29T10:00:13.012023+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.254s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 254.33}, "user_id": null, "request_id": "4ef29c0f-4cde-4605-9323-a0f0c725c7a5"}
{"timestamp": "2025-05-29T10:00:14.025552+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.44}, "user_id": null, "request_id": "e2cee2d2-60a3-4fb1-aa4d-5bcde334ab81"}
{"timestamp": "2025-05-29T10:00:15.964564+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (1.924s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 1924.17}, "user_id": null, "request_id": "9213803f-7725-4ff0-9fe6-b3d6106f605f"}
{"timestamp": "2025-05-29T10:00:15.968552+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (1.927s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 1926.67}, "user_id": null, "request_id": "e7cedb02-eb5b-4c82-84ae-c8d45c34ee59"}
{"timestamp": "2025-05-29T10:00:16.549321+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 5.35}, "user_id": null, "request_id": "d6122ae8-6a2f-4ffe-b8c2-ed2b26dee41f"}
{"timestamp": "2025-05-29T10:00:16.565576+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 4.72}, "user_id": null, "request_id": "5d1f4098-2e54-49a3-8161-7a46e07f888d"}
{"timestamp": "2025-05-29T10:00:21.055259+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (4.487s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4486.87}, "user_id": null, "request_id": "97b1576a-161f-4ce4-ab20-8fafc257cf6f"}
{"timestamp": "2025-05-29T10:00:25.353993+00:00", "level": "INFO", "logger": "api", "message": "DELETE /stores/3 - 200 (1.712s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "DELETE", "path": "/stores/3", "status_code": 200, "duration_ms": 1712.21}, "user_id": null, "request_id": "a5caae0e-c03f-46db-bd0a-067e4d3c4175"}
{"timestamp": "2025-05-29T10:00:25.372079+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 14.71}, "user_id": null, "request_id": "6a12c5b6-0425-40e2-a607-f8fabfe0bb98"}
{"timestamp": "2025-05-29T10:00:49.389597+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.77}, "user_id": null, "request_id": "d50efa2e-bc22-42a8-a87d-e9d98320c79e"}
{"timestamp": "2025-05-29T10:00:49.521529+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/styles.css - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/styles.css", "status_code": 200, "duration_ms": 1.68}, "user_id": null, "request_id": "b9bd5e34-585b-4af3-aee0-35765e3c4413"}
{"timestamp": "2025-05-29T10:00:49.539738+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 200, "duration_ms": 1.05}, "user_id": null, "request_id": "3aa466c6-ed23-441f-be93-0766f4653ddc"}
{"timestamp": "2025-05-29T10:00:49.547319+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/auth.js - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/auth.js", "status_code": 200, "duration_ms": 1.29}, "user_id": null, "request_id": "745da73a-6f3f-4a23-9b25-13764ebe932e"}
{"timestamp": "2025-05-29T10:00:49.678417+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.43}, "user_id": null, "request_id": "54e80e32-5d1c-472b-9c5c-eed4d0263022"}
{"timestamp": "2025-05-29T10:00:56.999964+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 403 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 403, "duration_ms": 0.53}, "user_id": null, "request_id": "6d2934eb-4ef3-45da-9f79-28b2c1e27297"}
{"timestamp": "2025-05-29T10:01:05.751773+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.65}, "user_id": null, "request_id": "41118ac4-4b31-49af-adb5-d9f5c55a6efc"}
{"timestamp": "2025-05-29T10:01:05.928038+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.16}, "user_id": null, "request_id": "7aea84fa-c634-4194-9874-f2c811d9b0f3"}
{"timestamp": "2025-05-29T10:01:17.447350+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.83}, "user_id": null, "request_id": "007040f3-da1a-414e-89f4-813d26e47c43"}
{"timestamp": "2025-05-29T10:01:52.225332+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.202s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 202.15}, "user_id": null, "request_id": "c835255d-fe28-4cde-a8cc-625d0a0ba97a"}
{"timestamp": "2025-05-29T10:01:55.180247+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.06}, "user_id": null, "request_id": "1b86c50e-f6cb-4979-aff0-7f7ee17b4921"}
{"timestamp": "2025-05-29T10:01:55.286477+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 7.11}, "user_id": null, "request_id": "755120f1-a6b6-4282-b363-ed9cdd2fad29"}
{"timestamp": "2025-05-29T10:01:55.291878+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 11.24}, "user_id": null, "request_id": "b4b6411c-8dd3-4189-8c0f-7c3ed8f95d45"}
{"timestamp": "2025-05-29T10:01:57.587171+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.01}, "user_id": null, "request_id": "389a7d46-d154-4409-a32a-3bb152ba0601"}
{"timestamp": "2025-05-29T10:01:59.262545+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.49}, "user_id": null, "request_id": "548bedee-543a-419e-87be-ed991d5917b7"}
{"timestamp": "2025-05-29T10:02:06.484993+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 403 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 403, "duration_ms": 0.81}, "user_id": null, "request_id": "7bd0cd42-c65d-439c-a5ec-210ba78cd617"}
{"timestamp": "2025-05-29T10:06:31.439530+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 5.43}, "user_id": null, "request_id": "a1c9b3c3-ec89-4df1-96c6-75a66c5076c1"}
{"timestamp": "2025-05-29T10:11:39.405329+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 5.52}, "user_id": null, "request_id": "fc7f7b6d-190d-404a-aa18-b5657c070612"}
{"timestamp": "2025-05-29T10:11:41.800986+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.269s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 268.82}, "user_id": null, "request_id": "84baa74e-98b8-4722-b037-6d09011c4142"}
{"timestamp": "2025-05-29T10:11:42.889668+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 3.79}, "user_id": null, "request_id": "bc6bfb4b-4db9-4193-a743-84af0a91d806"}
{"timestamp": "2025-05-29T10:11:42.922310+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 14.94}, "user_id": null, "request_id": "3aca2303-1d19-4850-ab54-535b0dabc042"}
{"timestamp": "2025-05-29T10:11:42.923119+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.015s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 14.9}, "user_id": null, "request_id": "2a2dea97-4cba-4f9d-b126-b7de59c9687e"}
{"timestamp": "2025-05-29T10:11:44.063221+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 4.81}, "user_id": null, "request_id": "91f1c114-2275-45be-9589-717596b5a4be"}
{"timestamp": "2025-05-29T10:11:44.077488+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 200, "duration_ms": 6.9}, "user_id": null, "request_id": "2a9f6892-233c-4659-a362-038b7d6bd703"}
{"timestamp": "2025-05-29T10:11:44.084558+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.58}, "user_id": null, "request_id": "bf20c8db-f6fc-435c-88f8-41d762b55f35"}
{"timestamp": "2025-05-29T10:11:52.897003+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 200, "duration_ms": 6.0}, "user_id": null, "request_id": "2a38db0f-0ac9-4ff7-9ca0-c040d38ee5e2"}
{"timestamp": "2025-05-29T10:13:02.193237+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 200, "duration_ms": 4.29}, "user_id": null, "request_id": "63d020d4-f94d-4e7b-865e-97dd8504473e"}
{"timestamp": "2025-05-29T10:15:09.211976+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/callback - 307 (120.919s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/callback", "status_code": 307, "duration_ms": 120919.27}, "user_id": null, "request_id": "bfa0a904-0ac0-4e2b-9f52-a0bc07fdf8e6"}
{"timestamp": "2025-05-29T10:15:09.287202+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 0.79}, "user_id": null, "request_id": "d6b6735f-9e0e-4ba3-84fb-cd34a46a1473"}
{"timestamp": "2025-05-29T10:15:17.524278+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.49}, "user_id": null, "request_id": "94a365f2-d39b-49b5-88d4-c975d8c6988b"}
{"timestamp": "2025-05-29T10:15:17.653105+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 7.46}, "user_id": null, "request_id": "51dcfb8a-c075-4c5a-af3d-17dbca185c1c"}
{"timestamp": "2025-05-29T10:15:17.664165+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 11.3}, "user_id": null, "request_id": "d8798359-9a37-4559-8219-3d1c07119009"}
{"timestamp": "2025-05-29T10:53:31.916080+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 4.42}, "user_id": null, "request_id": "2752bc5e-a361-4920-9065-5f729f8d8e42"}
{"timestamp": "2025-05-29T10:53:33.403114+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 3.51}, "user_id": null, "request_id": "b00da86b-3faa-4987-840a-fc7698fc87a8"}
{"timestamp": "2025-05-29T10:53:33.429566+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.010s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 9.59}, "user_id": null, "request_id": "fc8e4950-e8e7-420e-a6a4-64f7c7cacb17"}
{"timestamp": "2025-05-29T10:53:33.430034+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 9.19}, "user_id": null, "request_id": "d4a933e0-12ae-4f0a-be12-dd915d0e7c73"}
{"timestamp": "2025-05-29T10:53:33.435849+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.86}, "user_id": null, "request_id": "4cdba66c-cf34-4674-a207-d094db38f2ce"}
{"timestamp": "2025-05-29T10:53:35.050339+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.238s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 237.93}, "user_id": null, "request_id": "2642a182-d42a-4b4b-8190-f2e587ccbc33"}
{"timestamp": "2025-05-29T10:53:36.135288+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.32}, "user_id": null, "request_id": "18bcf1ef-8acb-48f0-9e2c-5ddb1d9a7e8e"}
{"timestamp": "2025-05-29T10:53:36.159821+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7.76}, "user_id": null, "request_id": "c26e6ffd-5074-4e32-b8f4-46ecb16d0e74"}
{"timestamp": "2025-05-29T10:53:36.160056+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 7.5}, "user_id": null, "request_id": "d14701f1-51b8-4fec-855c-e2f951a4e873"}
{"timestamp": "2025-05-29T10:53:53.007857+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.5}, "user_id": null, "request_id": "b422cff6-bdcf-4d58-bdd4-ffc1a6597f2a"}
{"timestamp": "2025-05-29T10:53:55.895477+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.202s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 202.43}, "user_id": null, "request_id": "826c4a7c-23ba-41c3-ab87-3c05bcc4a515"}
{"timestamp": "2025-05-29T10:53:56.915950+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.48}, "user_id": null, "request_id": "0a0dd6ac-9118-412b-b826-d362fe104d93"}
{"timestamp": "2025-05-29T10:53:56.935736+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 10.53}, "user_id": null, "request_id": "2ebd04cd-9a0e-4cb1-8f89-75dc72762547"}
{"timestamp": "2025-05-29T10:53:56.936094+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 10.78}, "user_id": null, "request_id": "8c612a18-4dea-4c3d-9ae5-ad3b25afb930"}
{"timestamp": "2025-05-29T10:53:58.461487+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 9.4}, "user_id": null, "request_id": "866cb709-c769-4627-9c78-8f49bcf16ffd"}
{"timestamp": "2025-05-29T10:53:58.477681+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 4.12}, "user_id": null, "request_id": "58cb3f4a-f844-4fc8-8489-33d0a62c2a36"}
{"timestamp": "2025-05-29T10:53:58.484059+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.51}, "user_id": null, "request_id": "1080fd03-b1c3-414b-bfba-8d8d3509a4c7"}
{"timestamp": "2025-05-29T10:54:03.542846+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 4.78}, "user_id": null, "request_id": "764e8a9b-40d4-4fc1-9eb6-e465823a305c"}
{"timestamp": "2025-05-29T10:54:03.554519+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 4.63}, "user_id": null, "request_id": "7c79250c-f6d7-49db-82e5-0565b3850b73"}
{"timestamp": "2025-05-29T10:54:11.423474+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 1.09}, "user_id": null, "request_id": "dde9b826-3584-40ac-a22d-60d75e16c650"}
{"timestamp": "2025-05-29T10:54:11.442691+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.68}, "user_id": null, "request_id": "66c94115-1d15-4aa0-9615-2d322efe1ed3"}
{"timestamp": "2025-05-29T11:02:27.408943+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 7.43}, "user_id": null, "request_id": "d84827c7-3b9d-43aa-ab01-11d42e50d38a"}
{"timestamp": "2025-05-29T11:02:27.443809+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.020s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 19.85}, "user_id": null, "request_id": "6f366793-64d4-4fcb-9cbf-419484822ec6"}
{"timestamp": "2025-05-29T11:02:27.444788+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.020s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 19.6}, "user_id": null, "request_id": "2f8d026c-8e14-48b1-baff-2f7b59be9bc1"}
{"timestamp": "2025-05-29T11:02:29.197526+00:00", "level": "INFO", "logger": "api", "message": "GET /profile - 200 (0.010s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/profile", "status_code": 200, "duration_ms": 9.93}, "user_id": null, "request_id": "689ac535-19ca-46a8-9f2a-1ee9981e1c7b"}
{"timestamp": "2025-05-29T11:02:29.206924+00:00", "level": "INFO", "logger": "api", "message": "GET /static/css/style.css - 404 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/css/style.css", "status_code": 404, "duration_ms": 1.73}, "user_id": null, "request_id": "778b22d2-3438-4fa2-ae03-8c8992ec663f"}
{"timestamp": "2025-05-29T11:02:29.219425+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3.76}, "user_id": null, "request_id": "2ac16147-70dd-4b33-9eaa-e077153d83bb"}
{"timestamp": "2025-05-29T11:02:29.229921+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/", "status_code": 200, "duration_ms": 4.73}, "user_id": null, "request_id": "87f58e8c-f789-4d36-9c0f-a48657952a0f"}
{"timestamp": "2025-05-29T11:02:31.150716+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.59}, "user_id": null, "request_id": "c1433947-88cd-4141-a96e-5e01da4db322"}
{"timestamp": "2025-05-29T11:02:31.170552+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 8.51}, "user_id": null, "request_id": "1f5c7e54-e59c-4f48-a592-2d132ad5480a"}
{"timestamp": "2025-05-29T11:02:31.171080+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 8.52}, "user_id": null, "request_id": "89bf2f7b-cccc-4344-9f3a-75ca4ceec683"}
{"timestamp": "2025-05-29T11:02:32.075841+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.53}, "user_id": null, "request_id": "37923c07-dcb5-4448-b8a0-12a496c63a44"}
{"timestamp": "2025-05-29T11:02:33.843581+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.216s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 216.13}, "user_id": null, "request_id": "93ee86e2-d8f9-4ea2-bce5-c1b6341f8faf"}
{"timestamp": "2025-05-29T11:02:34.859382+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.0}, "user_id": null, "request_id": "eace9005-711c-474d-a7f2-614d53014a9e"}
{"timestamp": "2025-05-29T11:02:34.891460+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7.85}, "user_id": null, "request_id": "1b0b504d-30c5-4ff1-8963-34aa2f398fcb"}
{"timestamp": "2025-05-29T11:02:34.891945+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 8.27}, "user_id": null, "request_id": "bd64b585-304f-4167-ba5c-affff6f577f0"}
{"timestamp": "2025-05-29T11:03:36.089052+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 2.17}, "user_id": null, "request_id": "dbd64d43-e8f1-4c17-b23e-e8cba6f424c1"}
{"timestamp": "2025-05-29T11:03:36.099794+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 1.24}, "user_id": null, "request_id": "9d91ce9f-3d9c-49fd-ae25-b621cb38f52e"}
{"timestamp": "2025-05-29T11:03:36.106777+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 5.09}, "user_id": null, "request_id": "8d24c995-dd38-418c-94e4-b1734c83b431"}
{"timestamp": "2025-05-29T11:03:42.027368+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 200, "duration_ms": 6.12}, "user_id": null, "request_id": "bf7f0f68-d509-4507-bbac-1bf60ca140b9"}
{"timestamp": "2025-05-29T11:03:47.027819+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/callback - 307 (2.525s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/callback", "status_code": 307, "duration_ms": 2524.79}, "user_id": null, "request_id": "6355eae9-a072-49c1-b0c4-144227b8da6f"}
{"timestamp": "2025-05-29T11:03:47.100483+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 0.64}, "user_id": null, "request_id": "d9e66bcc-f83f-4582-8c49-f2230bd8be2b"}
{"timestamp": "2025-05-29T11:04:40.944116+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.56}, "user_id": null, "request_id": "fce8a811-3e73-4ef6-8f96-07ca3f54c9a8"}
{"timestamp": "2025-05-29T11:04:41.163549+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 2.87}, "user_id": null, "request_id": "dcf34802-03cb-4447-9b8e-bcb1ba5a0f8a"}
{"timestamp": "2025-05-29T11:04:41.166186+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 401 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 401, "duration_ms": 1.6}, "user_id": null, "request_id": "882b12b5-b932-4cb5-959f-49f3dbf2ed73"}
{"timestamp": "2025-05-29T11:04:41.223624+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.61}, "user_id": null, "request_id": "e5f85050-1303-4961-8b76-efdd4ef391da"}
{"timestamp": "2025-05-29T11:05:34.261842+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 4.28}, "user_id": null, "request_id": "d0489c0b-1c10-4024-88b1-f28acf914e09"}
{"timestamp": "2025-05-29T11:05:36.328323+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 6.61}, "user_id": null, "request_id": "79757508-18c3-4cfc-8c3c-81f87677008b"}
{"timestamp": "2025-05-29T11:05:36.388253+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.033s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 32.78}, "user_id": null, "request_id": "938b15e5-e857-46ec-8984-7080455b6d17"}
{"timestamp": "2025-05-29T11:05:36.388484+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.033s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 32.62}, "user_id": null, "request_id": "b50e1c39-22db-4b47-bb6f-fae48f70090a"}
{"timestamp": "2025-05-29T11:05:38.861268+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 6.56}, "user_id": null, "request_id": "8fe9fee6-8675-4b47-b0d0-e1626bde466a"}
{"timestamp": "2025-05-29T11:05:38.880601+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.01}, "user_id": null, "request_id": "d807b04b-b356-4774-8381-90d8453d97ce"}
{"timestamp": "2025-05-29T11:05:42.532593+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 0.63}, "user_id": null, "request_id": "702d05dc-5caf-4123-847b-37c61d7681c9"}
{"timestamp": "2025-05-29T11:05:42.552334+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 6.53}, "user_id": null, "request_id": "1948a37e-cf3b-4c25-b118-530f27dd7922"}
{"timestamp": "2025-05-29T11:21:55.545267+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 5.68}, "user_id": null, "request_id": "e0ed61ac-ad90-4905-81fc-9eb1a5abd586"}
{"timestamp": "2025-05-29T11:21:55.590765+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 29.09}, "user_id": null, "request_id": "21880c5d-be90-4110-b42b-a7d834911a99"}
{"timestamp": "2025-05-29T11:21:55.591093+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.029s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 28.79}, "user_id": null, "request_id": "13478dfd-7452-4600-8c43-d0c10c9e9125"}
{"timestamp": "2025-05-29T11:21:56.355790+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 4.34}, "user_id": null, "request_id": "25369e7f-7aea-41e5-8f9e-27b06c039bb4"}
{"timestamp": "2025-05-29T11:21:58.198940+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.227s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 226.96}, "user_id": null, "request_id": "74670ef2-7799-47fc-a281-c4eff8a133fd"}
{"timestamp": "2025-05-29T11:21:59.375443+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.91}, "user_id": null, "request_id": "2b535ad3-2382-404e-a6b7-aaa79b691656"}
{"timestamp": "2025-05-29T11:21:59.397167+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 9.17}, "user_id": null, "request_id": "f5d4bc21-7387-4262-b0fd-9cc52cccb7d2"}
{"timestamp": "2025-05-29T11:21:59.400670+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2.87}, "user_id": null, "request_id": "9c25a026-661c-48c5-8792-cb513085cc80"}
{"timestamp": "2025-05-29T11:22:02.888147+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 10.52}, "user_id": null, "request_id": "f2bfb3db-66d2-4d23-b731-59450a38c38b"}
{"timestamp": "2025-05-29T11:22:02.904981+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.008s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 8.43}, "user_id": null, "request_id": "d996f92f-5bb7-481a-b2da-0760c108cee1"}
{"timestamp": "2025-05-29T11:22:02.911248+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4.0}, "user_id": null, "request_id": "318f6c46-10bb-465b-8627-eb7011759031"}
{"timestamp": "2025-05-29T11:22:08.684221+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/install - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/install", "status_code": 200, "duration_ms": 6.8}, "user_id": null, "request_id": "57898e1b-8d04-4206-8176-574c80a52744"}
{"timestamp": "2025-05-29T11:24:13.514355+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/oauth/callback - 307 (101.602s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/oauth/callback", "status_code": 307, "duration_ms": 101602.36}, "user_id": null, "request_id": "3e4e60f8-352b-469b-bf77-eaef52847735"}
{"timestamp": "2025-05-29T11:24:21.768267+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 6.91}, "user_id": null, "request_id": "73a619b9-7aa3-42e4-87d3-8655bd4ebc52"}
{"timestamp": "2025-05-29T11:25:04.179375+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 0.87}, "user_id": null, "request_id": "21228ff2-7cd9-4c77-9a5c-bd71d8701f8d"}
{"timestamp": "2025-05-29T11:25:19.173127+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 401 (0.204s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 401, "duration_ms": 203.82}, "user_id": null, "request_id": "185a87a3-49f0-4a58-b320-78d2ab7110d4"}
{"timestamp": "2025-05-29T11:25:24.366486+00:00", "level": "INFO", "logger": "api", "message": "GET / - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/", "status_code": 200, "duration_ms": 0.65}, "user_id": null, "request_id": "821d7f9e-536a-4b16-9e7c-151bf4196042"}
{"timestamp": "2025-05-29T11:25:27.128149+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.63}, "user_id": null, "request_id": "0d71d2c8-814e-4ba2-8067-c58fe4abc253"}
{"timestamp": "2025-05-29T11:25:27.160539+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 11.68}, "user_id": null, "request_id": "b37b2ec9-b488-4f26-8ef3-4213e10c3dca"}
{"timestamp": "2025-05-29T11:25:27.161099+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (0.012s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 12.02}, "user_id": null, "request_id": "a3b65315-e58f-47b8-b3bd-8da26024236b"}
{"timestamp": "2025-05-29T11:35:44.342144+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 403 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 403, "duration_ms": 0.39}, "user_id": null, "request_id": "4760e682-3556-4fbf-9e70-6a2b8d8f69be"}
{"timestamp": "2025-05-29T11:36:14.605900+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 6.81}, "user_id": null, "request_id": "5f4ba837-cd48-4d63-9d3c-bd0bd5edda3b"}
{"timestamp": "2025-05-29T11:36:17.132232+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.506s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2505.88}, "user_id": null, "request_id": "f034bfdb-c926-4e9d-b2f1-05d8a6587d51"}
{"timestamp": "2025-05-29T11:36:17.133002+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (2.505s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 2505.42}, "user_id": null, "request_id": "d928f62b-a316-4d3e-9c10-dcab976755bc"}
{"timestamp": "2025-05-29T11:36:17.133561+00:00", "level": "INFO", "logger": "api", "message": "GET /.well-known/appspecific/com.chrome.devtools.json - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/.well-known/appspecific/com.chrome.devtools.json", "status_code": 404, "duration_ms": 1.49}, "user_id": null, "request_id": "5035cc65-5e68-4eb6-88c9-6c279a186044"}
{"timestamp": "2025-05-29T11:36:21.101177+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.005s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 5.5}, "user_id": null, "request_id": "2392c246-a5ae-484a-af08-e7dec63f0f68"}
{"timestamp": "2025-05-29T11:36:21.112849+00:00", "level": "INFO", "logger": "api", "message": "GET /.well-known/appspecific/com.chrome.devtools.json - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/.well-known/appspecific/com.chrome.devtools.json", "status_code": 404, "duration_ms": 1.3}, "user_id": null, "request_id": "7ae78ae3-e7f9-4d94-8e04-aaf3ce43a550"}
{"timestamp": "2025-05-29T11:36:30.236659+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (9.124s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 9124.05}, "user_id": null, "request_id": "6e6fb777-dfd9-4ebd-8dc8-2eda91dbda5e"}
{"timestamp": "2025-05-29T11:36:30.241661+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (9.126s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 9125.69}, "user_id": null, "request_id": "92f1a28c-9a13-4f36-922c-e893eaefb5f0"}
{"timestamp": "2025-05-29T11:36:30.243139+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 6.82}, "user_id": null, "request_id": "92b7922d-4335-4d32-8cc1-a860aef6c6b7"}
{"timestamp": "2025-05-29T11:36:30.254842+00:00", "level": "INFO", "logger": "api", "message": "GET /.well-known/appspecific/com.chrome.devtools.json - 404 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/.well-known/appspecific/com.chrome.devtools.json", "status_code": 404, "duration_ms": 0.27}, "user_id": null, "request_id": "96f6336d-ada1-4aa2-8788-64d843e13bd3"}
{"timestamp": "2025-05-29T11:36:36.219191+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.227s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 227.13}, "user_id": null, "request_id": "728214d5-f959-4968-9123-c26aa36049d2"}
{"timestamp": "2025-05-29T11:36:37.230961+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 1.27}, "user_id": null, "request_id": "a6954e0a-1f71-400e-9893-864593aa15da"}
{"timestamp": "2025-05-29T11:36:37.241257+00:00", "level": "INFO", "logger": "api", "message": "GET /.well-known/appspecific/com.chrome.devtools.json - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/.well-known/appspecific/com.chrome.devtools.json", "status_code": 404, "duration_ms": 1.47}, "user_id": null, "request_id": "fffe128a-a2f2-459d-81e2-e0a58e92adcb"}
{"timestamp": "2025-05-29T11:36:41.530388+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (4.286s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 4286.2}, "user_id": null, "request_id": "7b6a6973-f89e-4e1c-be95-477f3827d4f6"}
{"timestamp": "2025-05-29T11:36:41.530854+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (4.280s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 4279.52}, "user_id": null, "request_id": "27b93ec3-29ae-4dd5-9bc5-f5574de11e20"}
{"timestamp": "2025-05-29T11:36:44.630952+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.000s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.28}, "user_id": null, "request_id": "0e508e57-1317-4bc3-afab-84392f3ba5d5"}
{"timestamp": "2025-05-29T11:36:44.642831+00:00", "level": "INFO", "logger": "api", "message": "GET /.well-known/appspecific/com.chrome.devtools.json - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/.well-known/appspecific/com.chrome.devtools.json", "status_code": 404, "duration_ms": 1.15}, "user_id": null, "request_id": "e75bbfde-cde6-46ca-a4e1-8ad09eac31c6"}
{"timestamp": "2025-05-29T11:36:52.052023+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (7.405s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 7405.03}, "user_id": null, "request_id": "53da3045-4b5b-4d43-a449-f122c784c696"}
{"timestamp": "2025-05-29T11:36:52.054597+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (7.406s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 7405.62}, "user_id": null, "request_id": "1ba237f6-a3f6-48c3-8292-344af1eaaf69"}
{"timestamp": "2025-05-29T11:36:52.057967+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.013s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 12.83}, "user_id": null, "request_id": "2ad5ed95-6e04-44d7-80ec-92bd4c09de1f"}
{"timestamp": "2025-05-29T11:36:52.078392+00:00", "level": "INFO", "logger": "api", "message": "GET /.well-known/appspecific/com.chrome.devtools.json - 404 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/.well-known/appspecific/com.chrome.devtools.json", "status_code": 404, "duration_ms": 1.93}, "user_id": null, "request_id": "ede2b2f2-00b4-45c7-a9f7-74b265124ac4"}
{"timestamp": "2025-05-29T11:36:52.083445+00:00", "level": "INFO", "logger": "api", "message": "GET /login - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/login", "status_code": 200, "duration_ms": 1.38}, "user_id": null, "request_id": "226d06cc-af06-4a55-8d34-e04ba55e42e7"}
{"timestamp": "2025-05-29T11:36:52.085517+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 401 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 401, "duration_ms": 2.64}, "user_id": null, "request_id": "38d2e3d9-3dbc-471e-9e88-2868a2eab83d"}
{"timestamp": "2025-05-29T11:36:52.096768+00:00", "level": "INFO", "logger": "api", "message": "GET /.well-known/appspecific/com.chrome.devtools.json - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/.well-known/appspecific/com.chrome.devtools.json", "status_code": 404, "duration_ms": 0.92}, "user_id": null, "request_id": "91023bf8-6f72-48a2-9944-13ebd6c2cee4"}
{"timestamp": "2025-05-29T11:36:56.445779+00:00", "level": "INFO", "logger": "api", "message": "POST /auth/login - 200 (0.220s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/auth/login", "status_code": 200, "duration_ms": 219.56}, "user_id": null, "request_id": "76f4d701-9346-43e2-9142-3ca8801b53dc"}
{"timestamp": "2025-05-29T11:36:57.461449+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.004s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 4.0}, "user_id": null, "request_id": "a6e59b96-3e30-46ac-a022-0addad5e7ecf"}
{"timestamp": "2025-05-29T11:36:57.476177+00:00", "level": "INFO", "logger": "api", "message": "GET /.well-known/appspecific/com.chrome.devtools.json - 404 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/.well-known/appspecific/com.chrome.devtools.json", "status_code": 404, "duration_ms": 1.13}, "user_id": null, "request_id": "fc82ebd2-3c5f-4e76-baa3-db8c44ec0a86"}
{"timestamp": "2025-05-29T11:37:15.507680+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (18.024s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 18024.19}, "user_id": null, "request_id": "e77e3cdb-82fd-40b1-858b-a24f2f8bfc33"}
{"timestamp": "2025-05-29T11:37:15.509763+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (18.028s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 18027.6}, "user_id": null, "request_id": "3b5a3225-34f3-44b9-a082-0aab27065516"}
{"timestamp": "2025-05-29T11:37:24.976305+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 7.22}, "user_id": null, "request_id": "09d55ac0-2f96-4e83-8e16-48da8d245365"}
{"timestamp": "2025-05-29T11:37:25.005315+00:00", "level": "INFO", "logger": "api", "message": "GET /static/js/stores.js - 304 (0.006s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/static/js/stores.js", "status_code": 304, "duration_ms": 6.47}, "user_id": null, "request_id": "a22a8044-3c4d-4c87-90ae-91bd9df676c6"}
{"timestamp": "2025-05-29T11:37:38.515482+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (13.507s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 13507.49}, "user_id": null, "request_id": "01f2c553-c5f7-4f1f-8229-912b928b90e1"}
{"timestamp": "2025-05-29T11:37:38.517809+00:00", "level": "INFO", "logger": "api", "message": "GET /.well-known/appspecific/com.chrome.devtools.json - 404 (0.002s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/.well-known/appspecific/com.chrome.devtools.json", "status_code": 404, "duration_ms": 2.32}, "user_id": null, "request_id": "97432960-ea7e-4cfa-866e-185caebdbbc1"}
{"timestamp": "2025-05-29T11:37:44.546062+00:00", "level": "INFO", "logger": "api", "message": "GET /dashboard - 200 (0.001s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/dashboard", "status_code": 200, "duration_ms": 0.7}, "user_id": null, "request_id": "255d8075-83be-4b60-9ae1-6614178ff916"}
{"timestamp": "2025-05-29T11:37:58.413767+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (13.845s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 13844.77}, "user_id": null, "request_id": "17367b12-3b90-43aa-803a-60544c9e1f69"}
{"timestamp": "2025-05-29T11:37:58.419471+00:00", "level": "INFO", "logger": "api", "message": "GET /auth/validate-token - 200 (13.850s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/auth/validate-token", "status_code": 200, "duration_ms": 13850.12}, "user_id": null, "request_id": "7d65a7d5-5086-4378-9f4c-57dbef9fff33"}
{"timestamp": "2025-05-29T11:37:58.420180+00:00", "level": "INFO", "logger": "api", "message": "GET /chat - 200 (0.007s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/chat", "status_code": 200, "duration_ms": 6.5}, "user_id": null, "request_id": "4c3db156-cf3e-4755-81a5-76b9737eecab"}
{"timestamp": "2025-05-29T11:37:58.441470+00:00", "level": "INFO", "logger": "api", "message": "GET /assistants/stores/selection - 200 (0.011s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/assistants/stores/selection", "status_code": 200, "duration_ms": 10.64}, "user_id": null, "request_id": "c2a98418-faf7-4916-8cf0-b7c2aab2158f"}
{"timestamp": "2025-05-29T11:38:13.406082+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (9.421s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 9420.77}, "user_id": null, "request_id": "3fa48bfd-f9d8-4f48-bf29-09b9c04cb5c4"}
{"timestamp": "2025-05-29T11:38:31.444140+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (7.342s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 7341.73}, "user_id": null, "request_id": "aef1be7d-93ed-403b-9cee-443bc1814c95"}
{"timestamp": "2025-05-29T11:39:11.988353+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (4.280s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 4280.22}, "user_id": null, "request_id": "7391a9d8-85c2-43b7-b0a7-cc6cc5080b9b"}
{"timestamp": "2025-05-29T11:39:36.396310+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (7.897s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 7896.67}, "user_id": null, "request_id": "38b1244a-3201-4eb5-b209-947ff509d89b"}
{"timestamp": "2025-05-29T11:40:16.832552+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (5.380s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 5380.41}, "user_id": null, "request_id": "65ea8322-46fd-4bee-8077-8eaad5c709c5"}
{"timestamp": "2025-05-29T11:40:16.835727+00:00", "level": "INFO", "logger": "api", "message": "GET /stores - 200 (0.003s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores", "status_code": 200, "duration_ms": 3.41}, "user_id": null, "request_id": "576b032e-f892-4922-98c8-f5fe0e247f3b"}
{"timestamp": "2025-05-29T11:40:19.117980+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (2.241s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 2240.62}, "user_id": null, "request_id": "0263f5e0-213f-43ef-bc98-29a024b07471"}
{"timestamp": "2025-05-29T11:41:04.508557+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (10.367s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 10366.82}, "user_id": null, "request_id": "07ec6a7d-7045-44e0-8af3-f167ad090e71"}
{"timestamp": "2025-05-29T11:41:29.648247+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (4.439s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 4439.22}, "user_id": null, "request_id": "506a9893-1d0e-423d-9d9e-98365a12db58"}
{"timestamp": "2025-05-29T11:41:45.781350+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (5.994s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 5994.48}, "user_id": null, "request_id": "e25574f1-9b7e-4f8d-96cd-702077f60885"}
{"timestamp": "2025-05-29T11:42:06.197860+00:00", "level": "INFO", "logger": "api", "message": "POST /assistants/chat - 200 (10.521s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/assistants/chat", "status_code": 200, "duration_ms": 10520.67}, "user_id": null, "request_id": "78493be7-9f86-46f3-bf83-dbb1df8e1e14"}
{"timestamp": "2025-05-29T11:44:02.654679+00:00", "level": "INFO", "logger": "api", "message": "POST /stores/6/sync - 200 (57.009s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "POST", "path": "/stores/6/sync", "status_code": 200, "duration_ms": 57009.35}, "user_id": null, "request_id": "a0270aa5-2a86-429b-bb89-8d6bd498a40d"}
{"timestamp": "2025-05-29T11:44:07.086905+00:00", "level": "INFO", "logger": "api", "message": "GET /stores/ - 200 (3.668s)", "module": "logging_config", "function": "log_api_request", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "extra": {"method": "GET", "path": "/stores/", "status_code": 200, "duration_ms": 3668.16}, "user_id": null, "request_id": "1a8c9014-c0c6-4a6f-b9d5-22cfe10ae897"}
