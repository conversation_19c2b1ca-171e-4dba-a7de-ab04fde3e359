{"timestamp": "2025-05-27T08:53:54.906180+00:00", "level": "INFO", "logger": "sync", "message": "Store sync initiated", "module": "test_logging", "function": "test_production_logging", "line": 41, "thread": 8549255232, "thread_name": "MainThread", "extra": {"operation": "full_sync", "force": false}, "user_id": 456, "store_id": 123}
{"timestamp": "2025-05-27T09:41:46.358522+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 223, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:42:13.890142+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 244, "thread": 8549255232, "thread_name": "MainThread", "extra": {"products_hash": "d839e162...", "orders_hash": "3a30bd8c...", "customers_hash": "472f1f7d..."}, "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:42:13.890439+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 260, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:42:13.890484+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 265, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:42:13.890513+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 269, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:42:13.890539+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 273, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:42:13.890566+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 298, "thread": 8549255232, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:43:20.153329+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 354, "thread": 8549255232, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 94.823662}, "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:47:57.346778+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:48:20.273339+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8549255232, "thread_name": "MainThread", "extra": {"products_hash": "d839e162...", "orders_hash": "3a30bd8c...", "customers_hash": "472f1f7d..."}, "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:48:20.274555+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:48:20.274619+00:00", "level": "INFO", "logger": "sync", "message": "No changes detected in any data type, skipping sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 258, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-27T09:51:33.177412+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:51:56.311111+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8549255232, "thread_name": "MainThread", "extra": {"products_hash": "d839e162...", "orders_hash": "3a30bd8c...", "customers_hash": "472f1f7d..."}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:51:56.311259+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:51:56.311315+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:51:56.311344+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:51:56.311372+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:51:56.311398+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8549255232, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:53:23.061531+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8549255232, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 110.74261}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:53:42.938628+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:54:07.623561+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8549255232, "thread_name": "MainThread", "extra": {"products_hash": "d839e162...", "orders_hash": "3a30bd8c...", "customers_hash": "472f1f7d..."}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:54:07.624415+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-27T09:54:07.624489+00:00", "level": "INFO", "logger": "sync", "message": "No changes detected in any data type, skipping sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 258, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T05:12:43.150806+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-28T05:13:17.262215+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8549255232, "thread_name": "MainThread", "extra": {"products_hash": "39d92bd2...", "orders_hash": "d59f25a1...", "customers_hash": "472f1f7d..."}, "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-28T05:13:17.263605+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-28T05:13:17.263672+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-28T05:13:17.263707+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8549255232, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-28T05:13:17.263740+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8549255232, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": false}, "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-28T05:13:51.086185+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8549255232, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders"], "duration": 68.638311}, "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-28T05:13:52.135637+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8549255232, "thread_name": "MainThread", "user_id": 3, "store_id": 8}
{"timestamp": "2025-05-28T05:14:24.560327+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8549255232, "thread_name": "MainThread", "extra": {"products_hash": "39d92bd2...", "orders_hash": "d59f25a1...", "customers_hash": "472f1f7d..."}, "user_id": 3, "store_id": 8}
{"timestamp": "2025-05-28T05:14:24.561732+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8549255232, "thread_name": "MainThread", "user_id": 3, "store_id": 8}
{"timestamp": "2025-05-28T05:14:24.561784+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8549255232, "thread_name": "MainThread", "user_id": 3, "store_id": 8}
{"timestamp": "2025-05-28T05:14:24.561818+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8549255232, "thread_name": "MainThread", "user_id": 3, "store_id": 8}
{"timestamp": "2025-05-28T05:14:24.561848+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8549255232, "thread_name": "MainThread", "user_id": 3, "store_id": 8}
{"timestamp": "2025-05-28T05:14:24.561900+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8549255232, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 3, "store_id": 8}
{"timestamp": "2025-05-28T05:15:13.231359+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8549255232, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 82.121524}, "user_id": 3, "store_id": 8}
{"timestamp": "2025-05-28T05:15:13.971308+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T05:15:51.532694+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8549255232, "thread_name": "MainThread", "extra": {"products_hash": "39d92bd2...", "orders_hash": "d59f25a1...", "customers_hash": "472f1f7d..."}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T05:15:51.533949+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T05:15:51.534014+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T05:15:51.534041+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8549255232, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T05:15:51.534065+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8549255232, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": false}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T05:16:28.095168+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8549255232, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders"], "duration": 74.82556}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T05:17:42.141110+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 6139244544, "thread_name": "Thread-1 (main)", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-28T07:04:45.737192+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T07:05:09.139361+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "225f3777...", "orders_hash": "f3d6f408...", "customers_hash": "24cdd12a..."}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T07:05:09.140223+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T07:05:09.140284+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T07:05:09.140316+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T07:05:09.140344+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T07:05:09.140372+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T07:05:37.800837+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 52.870571}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T08:04:00.393654+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T08:04:21.440249+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "24cdd12a..."}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T08:04:21.440382+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T08:04:21.440432+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T08:04:21.440472+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": false, "sync_customers": false}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T08:04:22.492335+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products"], "duration": 22.920684}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T09:59:55.385782+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 1}
{"timestamp": "2025-05-28T10:00:21.721791+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 1, "store_id": 1}
{"timestamp": "2025-05-28T10:00:21.722685+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 1}
{"timestamp": "2025-05-28T10:00:21.722745+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 1}
{"timestamp": "2025-05-28T10:00:21.722781+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 1}
{"timestamp": "2025-05-28T10:00:21.722814+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 1}
{"timestamp": "2025-05-28T10:00:21.722848+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 1}
{"timestamp": "2025-05-28T10:01:15.670058+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 81.03389}, "user_id": 1, "store_id": 1}
{"timestamp": "2025-05-28T10:05:36.033474+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 2}
{"timestamp": "2025-05-28T10:06:04.131687+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 2, "store_id": 2}
{"timestamp": "2025-05-28T10:06:04.131799+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 2}
{"timestamp": "2025-05-28T10:06:04.131838+00:00", "level": "INFO", "logger": "sync", "message": "No changes detected in any data type, skipping sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 258, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 2}
{"timestamp": "2025-05-28T10:12:47.151762+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:13:16.908129+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:13:16.909056+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:13:16.909116+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:13:16.909151+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:13:16.909184+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:13:16.909218+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:14:06.815381+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 79.638218}, "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:14:48.168311+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:15:22.529177+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:15:22.530090+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T10:15:22.530145+00:00", "level": "INFO", "logger": "sync", "message": "No changes detected in any data type, skipping sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 258, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 3}
{"timestamp": "2025-05-28T11:56:14.478687+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 7}
{"timestamp": "2025-05-28T11:57:07.125243+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 1, "store_id": 7}
{"timestamp": "2025-05-28T11:57:07.126656+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 7}
{"timestamp": "2025-05-28T11:57:07.126741+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 7}
{"timestamp": "2025-05-28T11:57:07.126783+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 7}
{"timestamp": "2025-05-28T11:57:07.126816+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 7}
{"timestamp": "2025-05-28T11:57:07.126861+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 7}
{"timestamp": "2025-05-28T11:58:03.175403+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 108.688826}, "user_id": 1, "store_id": 7}
{"timestamp": "2025-05-28T12:34:31.077274+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:35:07.082581+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:35:07.083560+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:35:07.083602+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:35:07.083630+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:35:07.083655+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:35:07.083680+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:36:13.829683+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 102.736394}, "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:38:03.317432+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:38:43.127421+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:38:43.128649+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:38:43.128701+00:00", "level": "INFO", "logger": "sync", "message": "No changes detected in any data type, skipping sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 258, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 8}
{"timestamp": "2025-05-28T12:47:23.434758+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 9}
{"timestamp": "2025-05-28T12:48:11.101311+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 1, "store_id": 9}
{"timestamp": "2025-05-28T12:48:11.102182+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 9}
{"timestamp": "2025-05-28T12:48:11.102249+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 9}
{"timestamp": "2025-05-28T12:48:11.102288+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 9}
{"timestamp": "2025-05-28T12:48:11.102323+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 9}
{"timestamp": "2025-05-28T12:48:11.102358+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 9}
{"timestamp": "2025-05-28T12:49:02.777699+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 99.316363}, "user_id": 1, "store_id": 9}
{"timestamp": "2025-05-28T13:06:11.272152+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 11}
{"timestamp": "2025-05-28T13:06:57.812148+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 2, "store_id": 11}
{"timestamp": "2025-05-28T13:06:57.813080+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 11}
{"timestamp": "2025-05-28T13:06:57.813132+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 11}
{"timestamp": "2025-05-28T13:06:57.813168+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 11}
{"timestamp": "2025-05-28T13:06:57.813200+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 11}
{"timestamp": "2025-05-28T13:06:57.813247+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 2, "store_id": 11}
{"timestamp": "2025-05-28T13:08:09.885373+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 118.601203}, "user_id": 2, "store_id": 11}
{"timestamp": "2025-05-28T13:15:04.541866+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T13:15:48.455877+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T13:15:48.456804+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T13:15:48.457096+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T13:15:48.457169+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T13:15:48.457212+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T13:15:48.457250+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T13:15:53.739974+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 49.189112}, "user_id": 1, "store_id": 12}
{"timestamp": "2025-05-28T13:47:25.270285+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 1}
{"timestamp": "2025-05-28T13:47:53.794455+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "74941b0c...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 2, "store_id": 1}
{"timestamp": "2025-05-28T13:47:53.795869+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 1}
{"timestamp": "2025-05-28T13:47:53.795929+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 246, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 1}
{"timestamp": "2025-05-28T13:47:53.795965+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 1}
{"timestamp": "2025-05-28T13:47:53.795999+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 254, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 1}
{"timestamp": "2025-05-28T13:47:53.796032+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 279, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 2, "store_id": 1}
{"timestamp": "2025-05-28T13:48:47.855582+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 322, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 82.55806}, "user_id": 2, "store_id": 1}
{"timestamp": "2025-05-28T16:03:36.107256+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 2}
{"timestamp": "2025-05-28T16:04:01.452735+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "a1df2f58...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 1, "store_id": 2}
{"timestamp": "2025-05-28T16:04:01.453685+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 2}
{"timestamp": "2025-05-28T16:04:01.453748+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 2}
{"timestamp": "2025-05-28T16:04:01.453778+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 257, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 2}
{"timestamp": "2025-05-28T16:04:01.453803+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 2}
{"timestamp": "2025-05-28T16:04:01.453828+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 289, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 2}
{"timestamp": "2025-05-28T16:04:48.888528+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 332, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": ["products", "orders", "customers"], "duration": 72.756934}, "user_id": 1, "store_id": 2}
{"timestamp": "2025-05-28T16:17:17.656090+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:17:41.934898+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "a1df2f58...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:17:41.936104+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:17:41.936153+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:17:41.936316+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 257, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:17:41.936351+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:17:41.936376+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 289, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:17:43.814709+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 332, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": [], "duration": 26.154929}, "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:40:16.251437+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:40:39.046343+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "a1df2f58...", "orders_hash": "f3d6f408...", "customers_hash": "6dd1cf04..."}, "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:40:39.046506+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 241, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:40:39.046555+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 250, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:40:39.046592+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 257, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:40:39.046626+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 264, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:40:39.046659+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 289, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-28T16:40:40.609344+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 332, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": [], "duration": 25.182421}, "user_id": 2, "store_id": 3}
{"timestamp": "2025-05-29T10:15:09.761733+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 6157561856, "thread_name": "Thread-1 (run_setup)", "user_id": 2, "store_id": 4}
{"timestamp": "2025-05-29T11:03:47.512755+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 211, "thread": 6186381312, "thread_name": "Thread-1 (run_setup)", "user_id": 1, "store_id": 5}
{"timestamp": "2025-05-29T11:04:41.418621+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 225, "thread": 6186381312, "thread_name": "Thread-1 (run_setup)", "extra": {"products_hash": "307ec713...", "orders_hash": "5fa1e2cc...", "customers_hash": "b5e5850b..."}, "user_id": 1, "store_id": 5}
{"timestamp": "2025-05-29T11:04:41.419529+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 289, "thread": 6186381312, "thread_name": "Thread-1 (run_setup)", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 5}
{"timestamp": "2025-05-29T11:04:47.556799+00:00", "level": "ERROR", "logger": "sync", "message": "Sync completed with errors", "module": "multi_store_assistant", "function": "sync_store_data", "line": 374, "thread": 6186381312, "thread_name": "Thread-1 (run_setup)", "extra": {"synced_files": [], "errors": ["Products chunk sync failed: Unknown error", "Orders chunk sync failed: Unknown error", "Customers chunk sync failed: Unknown error"], "duration": 59.919825}, "user_id": 1, "store_id": 5}
{"timestamp": "2025-05-29T11:24:21.774531+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 228, "thread": 6127497216, "thread_name": "Thread-1 (run_setup)", "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:25:04.622275+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 242, "thread": 6127497216, "thread_name": "Thread-1 (run_setup)", "extra": {"products_hash": "307ec713...", "orders_hash": "5fa1e2cc...", "customers_hash": "b5e5850b..."}, "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:25:04.622636+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 306, "thread": 6127497216, "thread_name": "Thread-1 (run_setup)", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:25:06.100339+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 349, "thread": 6127497216, "thread_name": "Thread-1 (run_setup)", "extra": {"synced_files": [], "duration": 44.317207}, "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:25:06.113754+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 228, "thread": 6127497216, "thread_name": "Thread-1 (run_setup)", "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:26:08.765147+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 242, "thread": 6127497216, "thread_name": "Thread-1 (run_setup)", "extra": {"products_hash": "307ec713...", "orders_hash": "5fa1e2cc...", "customers_hash": "b5e5850b..."}, "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:26:08.766431+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 306, "thread": 6127497216, "thread_name": "Thread-1 (run_setup)", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:26:10.206170+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 349, "thread": 6127497216, "thread_name": "Thread-1 (run_setup)", "extra": {"synced_files": [], "duration": 64.07032}, "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:43:14.577239+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 228, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:44:01.106579+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 242, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "307ec713...", "orders_hash": "5fa1e2cc...", "customers_hash": "b5e5850b..."}, "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:44:01.107549+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 258, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:44:01.107606+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 267, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:44:01.107642+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 274, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:44:01.107677+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 281, "thread": 8684030016, "thread_name": "MainThread", "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:44:01.107710+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 306, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T11:44:02.651206+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 349, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": [], "duration": 48.0659}, "user_id": 1, "store_id": 6}
{"timestamp": "2025-05-29T12:19:58.371966+00:00", "level": "INFO", "logger": "sync", "message": "Fetching data from Shopify for change detection", "module": "multi_store_assistant", "function": "sync_store_data", "line": 228, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-29T12:20:23.958601+00:00", "level": "INFO", "logger": "sync", "message": "Calculated data hashes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 242, "thread": 8684030016, "thread_name": "MainThread", "extra": {"products_hash": "bd3a4c28...", "orders_hash": "0b233968...", "customers_hash": "9d5eedd4..."}, "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-29T12:20:23.959698+00:00", "level": "INFO", "logger": "sync", "message": "Checking for individual data changes", "module": "multi_store_assistant", "function": "sync_store_data", "line": 258, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-29T12:20:23.959960+00:00", "level": "INFO", "logger": "sync", "message": "Products data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 267, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-29T12:20:23.960006+00:00", "level": "INFO", "logger": "sync", "message": "Orders data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 274, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-29T12:20:23.960046+00:00", "level": "INFO", "logger": "sync", "message": "Customers data changed, will sync", "module": "multi_store_assistant", "function": "sync_store_data", "line": 281, "thread": 8684030016, "thread_name": "MainThread", "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-29T12:20:23.960083+00:00", "level": "INFO", "logger": "sync", "message": "Performing selective sync for: products, orders, customers", "module": "multi_store_assistant", "function": "sync_store_data", "line": 306, "thread": 8684030016, "thread_name": "MainThread", "extra": {"sync_products": true, "sync_orders": true, "sync_customers": true}, "user_id": 2, "store_id": 7}
{"timestamp": "2025-05-29T12:20:25.463468+00:00", "level": "INFO", "logger": "sync", "message": "Sync completed successfully", "module": "multi_store_assistant", "function": "sync_store_data", "line": 349, "thread": 8684030016, "thread_name": "MainThread", "extra": {"synced_files": [], "duration": 28.635394}, "user_id": 2, "store_id": 7}
