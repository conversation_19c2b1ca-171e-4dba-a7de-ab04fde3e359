"""
Configuration Management Module

Centralized configuration management for the Pinecone E-commerce Assistant application.
Handles environment variables, OAuth settings, and application configuration.
"""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings:
    """Application settings and configuration"""
    
    def __init__(self):
        # Database Configuration
        self.db_user = os.getenv("DB_USER", "root")
        self.db_password = os.getenv("DB_PASSWORD", "")
        self.db_host = os.getenv("DB_HOST", "localhost")
        self.db_port = os.getenv("DB_PORT", "3306")
        self.db_name = os.getenv("DB_NAME", "pinecone_ecommerce")
        
        # JWT Configuration
        self.jwt_secret_key = os.getenv("JWT_SECRET_KEY", "your-super-secret-jwt-key-change-this-in-production")
        self.jwt_algorithm = os.getenv("JWT_ALGORITHM", "HS256")
        self.jwt_access_token_expire_minutes = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
        
        # Pinecone Configuration
        self.pinecone_api_key = os.getenv("PINECONE_API_KEY")
        
        # Shopify OAuth Configuration
        self.shopify_api_key = os.getenv("SHOPIFY_API_KEY")
        self.shopify_api_secret = os.getenv("SHOPIFY_API_SECRET")
        self.shopify_api_version = os.getenv("SHOPIFY_API_VERSION", "2025-01")
        self.shopify_scopes = os.getenv("SHOPIFY_SCOPES", "read_products,read_customers,read_orders")
        self.shopify_redirect_uri = os.getenv("SHOPIFY_REDIRECT_URI")
        self.shopify_graphql_uri = os.getenv("SHOPIFY_GRAPHQL_URI", "https://{shop}/admin/api/{version}/graphql.json")
        
        # Development Environment
        self.ngrok_base_url = os.getenv("NGROK_BASE_URL")
        
        # Application Settings
        self.app_name = os.getenv("APP_NAME", "Pinecone E-commerce Assistant")
        self.app_version = os.getenv("APP_VERSION", "2.0.0")
    
    @property
    def database_url(self) -> str:
        """Generate MySQL database URL"""
        from urllib.parse import quote_plus
        
        if self.db_password:
            password_encoded = quote_plus(self.db_password)
            return f"mysql+pymysql://{self.db_user}:{password_encoded}@{self.db_host}:{self.db_port}/{self.db_name}"
        else:
            return f"mysql+pymysql://{self.db_user}@{self.db_host}:{self.db_port}/{self.db_name}"
    
    def validate_oauth_config(self) -> bool:
        """Validate that all required OAuth configuration is present"""
        required_oauth_vars = [
            self.shopify_api_key,
            self.shopify_api_secret,
            self.shopify_redirect_uri,
            self.ngrok_base_url
        ]
        
        return all(var is not None and var.strip() != "" for var in required_oauth_vars)
    
    def get_oauth_install_url(self, shop: str) -> str:
        """Generate OAuth installation URL for a given shop"""
        if not self.validate_oauth_config():
            raise ValueError("OAuth configuration is incomplete. Check environment variables.")
        
        return (
            f"https://{shop}/admin/oauth/authorize?"
            f"client_id={self.shopify_api_key}&"
            f"scope={self.shopify_scopes}&"
            f"redirect_uri={self.shopify_redirect_uri}"
        )
    
    def get_shopify_graphql_url(self, shop: str) -> str:
        """Generate Shopify GraphQL URL for a given shop"""
        return self.shopify_graphql_uri.format(shop=shop, version=self.shopify_api_version)


# Global settings instance
settings = Settings()


class OAuthConfig:
    """OAuth-specific configuration helper"""
    
    @staticmethod
    def get_token_exchange_url(shop: str) -> str:
        """Get the token exchange URL for a shop"""
        return f"https://{shop}/admin/oauth/access_token"
    
    @staticmethod
    def get_token_exchange_payload(code: str) -> dict:
        """Get the payload for token exchange"""
        return {
            "client_id": settings.shopify_api_key,
            "client_secret": settings.shopify_api_secret,
            "code": code,
        }
    
    @staticmethod
    def get_graphql_headers(access_token: str) -> dict:
        """Get headers for Shopify GraphQL requests"""
        return {
            "X-Shopify-Access-Token": access_token,
            "Content-Type": "application/json"
        }


# Export commonly used configurations
__all__ = [
    "settings",
    "Settings",
    "OAuthConfig"
]
