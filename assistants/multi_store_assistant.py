from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timezone
from sqlalchemy.orm import Session
import logging

from pinecone_assistant import PineconeAssistant
from database.models import Store, Assistant
from database.crud import AssistantCRUD, StoreCRUD
from stores.shopify_client import extract_shop_name
from .models import AssistantStats, DataSyncResult
from logging_config import LoggerMixin, log_audit_event

# Get sync logger
logger = logging.getLogger('sync')

class MultiStoreAssistantManager:
    """Manager for multi-store assistant operations"""

    def __init__(self):
        self.active_assistants: Dict[int, PineconeAssistant] = {}
        self.assistant_cache_ttl = 3600  # 1 hour cache TTL
        self.assistant_cache_timestamps: Dict[int, datetime] = {}

    def generate_assistant_name(self, store: Store) -> str:
        """Generate unique assistant name for a Shopify store (shared across users)"""
        shop_name = extract_shop_name(store.shop_url)
        shopify_store_id = store.shopify_store_id

        # Format: ecommerce-assistant-{shop_name}-{shopify_store_id}
        # This ensures the same assistant name for all users connecting to the same Shopify store
        assistant_name = f"ecommerce-assistant-{shop_name}-{shopify_store_id}"

        # Ensure name is valid (alphanumeric with hyphens)
        assistant_name = assistant_name.lower().replace('_', '-')

        return assistant_name

    def create_assistant_for_store(self, db: Session, store: Store) -> Assistant:
        """Create a new assistant for a store or reuse existing shared assistant"""
        # Check if this specific store already has an assistant
        existing_assistant = AssistantCRUD.get_assistant_by_store(db, store.id)
        if existing_assistant:
            # Check if the existing assistant has the correct shared name
            correct_name = self.generate_assistant_name(store)
            if existing_assistant.assistant_name != correct_name:
                print(f"DEBUG: Updating assistant name from {existing_assistant.assistant_name} to {correct_name}")
                AssistantCRUD.update_assistant(db, existing_assistant.id, assistant_name=correct_name)
            return existing_assistant

        # Generate assistant name (shared across users for the same Shopify store)
        assistant_name = self.generate_assistant_name(store)

        # Check if a shared assistant already exists for this Shopify store
        # Look for any assistant with the same name (from any store with the same shopify_store_id)
        existing_shared_assistant = AssistantCRUD.get_assistant_by_name(db, assistant_name)

        if existing_shared_assistant and existing_shared_assistant.pinecone_assistant_id:
            # Only reuse if the existing assistant has a valid Pinecone ID
            # This prevents issues during account migration
            print(f"DEBUG: Reusing existing shared assistant {assistant_name} for store {store.id}")
            assistant = AssistantCRUD.create_assistant(
                db=db,
                store_id=store.id,
                assistant_name=assistant_name,
                pinecone_assistant_id=existing_shared_assistant.pinecone_assistant_id
            )
            # Copy sync status from the shared assistant
            AssistantCRUD.update_assistant(
                db, assistant.id,
                status=existing_shared_assistant.status,
                last_sync=existing_shared_assistant.last_sync,
                products_hash=existing_shared_assistant.products_hash,
                orders_hash=existing_shared_assistant.orders_hash,
                customers_hash=existing_shared_assistant.customers_hash,
                products_file_id=existing_shared_assistant.products_file_id,
                orders_file_id=existing_shared_assistant.orders_file_id,
                customers_file_id=existing_shared_assistant.customers_file_id
            )
            return assistant
        else:
            # Create a new assistant record in database
            # This handles both new stores and migration scenarios
            if existing_shared_assistant:
                print(f"DEBUG: Existing assistant {assistant_name} has invalid Pinecone ID, will recreate")
            assistant = AssistantCRUD.create_assistant(
                db=db,
                store_id=store.id,
                assistant_name=assistant_name
            )

        # Initialize Pinecone assistant
        try:
            # Decode store access token
            access_token = StoreCRUD.decode_token(store.access_token_encrypted)

            print(f"DEBUG: Creating Pinecone assistant for store {store.id} with name {assistant_name}")

            # Create Pinecone assistant instance
            pinecone_assistant = PineconeAssistant(
                assistant_name=assistant_name,
                shop_url=store.shop_url,
                access_token=access_token
            )

            print(f"DEBUG: Pinecone assistant created successfully for store {store.id}")

            # Update assistant with Pinecone ID if available
            if hasattr(pinecone_assistant, 'assistant') and pinecone_assistant.assistant:
                assistant_id = getattr(pinecone_assistant.assistant, 'id', None)
                if assistant_id:
                    print(f"DEBUG: Updating assistant record with Pinecone ID: {assistant_id}")
                    AssistantCRUD.update_assistant(
                        db, assistant.id,
                        pinecone_assistant_id=assistant_id,
                        status='active'
                    )
                else:
                    print("WARNING: Pinecone assistant created but no ID available")
            else:
                print("WARNING: Pinecone assistant object doesn't have assistant attribute")

            # Cache the assistant
            self.active_assistants[store.id] = pinecone_assistant
            self.assistant_cache_timestamps[store.id] = datetime.now(timezone.utc)

            print(f"DEBUG: Assistant cached successfully for store {store.id}")

        except Exception as e:
            print(f"ERROR: Failed to initialize Pinecone assistant for store {store.id}: {str(e)}")
            import traceback
            traceback.print_exc()
            # Update assistant status to error
            AssistantCRUD.update_assistant(
                db, assistant.id,
                status='error'
            )
            raise Exception(f"Failed to initialize Pinecone assistant: {str(e)}")

        return assistant

    def get_assistant_for_store(self, db: Session, store_id: int, user_id: int) -> Optional[PineconeAssistant]:
        """Get or create assistant for a store"""
        # Verify store ownership
        store = StoreCRUD.get_store_by_id(db, store_id)
        if not store or store.user_id != user_id:
            return None

        # Check cache first
        if store_id in self.active_assistants:
            cache_time = self.assistant_cache_timestamps.get(store_id)
            if cache_time and (datetime.now(timezone.utc) - cache_time).seconds < self.assistant_cache_ttl:
                return self.active_assistants[store_id]

        # Get or create assistant
        assistant = AssistantCRUD.get_assistant_by_store(db, store_id)
        if not assistant:
            assistant = self.create_assistant_for_store(db, store)

        # Initialize Pinecone assistant if not cached
        try:
            access_token = StoreCRUD.decode_token(store.access_token_encrypted)
            pinecone_assistant = PineconeAssistant(
                assistant_name=assistant.assistant_name,
                shop_url=store.shop_url,
                access_token=access_token
            )

            # Update assistant with Pinecone ID if available and not already set
            if (hasattr(pinecone_assistant, 'assistant') and pinecone_assistant.assistant and
                not assistant.pinecone_assistant_id):
                assistant_id = getattr(pinecone_assistant.assistant, 'id', None)
                if assistant_id:
                    print(f"DEBUG: Updating assistant record with Pinecone ID: {assistant_id}")
                    AssistantCRUD.update_assistant(
                        db, assistant.id,
                        pinecone_assistant_id=assistant_id,
                        status='active'
                    )

            # Cache the assistant
            self.active_assistants[store_id] = pinecone_assistant
            self.assistant_cache_timestamps[store_id] = datetime.now(timezone.utc)

            return pinecone_assistant

        except Exception as e:
            print(f"ERROR: Failed to initialize Pinecone assistant for store {store_id}: {str(e)}")
            # Update assistant status to error
            AssistantCRUD.update_assistant(db, assistant.id, status='error')
            return None

    def chat_with_store_assistant(self, db: Session, store_id: int, user_id: int,
                                 message: str, conversation_id: Optional[str] = None) -> Tuple[str, str]:
        """Chat with a store's assistant"""
        assistant = self.get_assistant_for_store(db, store_id, user_id)
        if not assistant:
            raise ValueError("Assistant not available for this store")

        try:
            response, conv_id = assistant.chat(message, conversation_id)
            return response, conv_id
        except Exception as e:
            raise Exception(f"Chat failed: {str(e)}")

    def sync_store_data(self, db: Session, store_id: int, user_id: int, force: bool = False) -> DataSyncResult:
        """Sync store data for assistant with change detection"""
        # Verify store ownership
        store = StoreCRUD.get_store_by_id(db, store_id)
        if not store or store.user_id != user_id:
            raise ValueError("Store not found or access denied")

        assistant_record = AssistantCRUD.get_assistant_by_store(db, store_id)
        if not assistant_record:
            assistant_record = self.create_assistant_for_store(db, store)

        # Update status to syncing
        AssistantCRUD.update_assistant(db, assistant_record.id, status='syncing')

        try:
            sync_start = datetime.now(timezone.utc)

            # Get assistant instance to fetch data
            assistant = self.get_assistant_for_store(db, store_id, user_id)
            if not assistant:
                raise Exception("Failed to get assistant instance")

            # Fetch current data from Shopify
            logger.info("Fetching data from Shopify for change detection", extra={
                'store_id': store_id, 'user_id': user_id
            })
            from shopify_api import fetch_products, fetch_orders, fetch_customers

            products_data = fetch_products(assistant.shopify_client)
            orders_data = fetch_orders(assistant.shopify_client)
            customers_data = fetch_customers(assistant.shopify_client)

            # Calculate hashes of current data
            products_hash = self._calculate_data_hash(products_data)
            orders_hash = self._calculate_data_hash(orders_data)
            customers_hash = self._calculate_data_hash(customers_data)

            logger.info(f"Calculated data hashes", extra={
                'store_id': store_id,
                'user_id': user_id,
                'extra_data': {
                    'products_hash': products_hash[:8] + '...',
                    'orders_hash': orders_hash[:8] + '...',
                    'customers_hash': customers_hash[:8] + '...'
                }
            })

            # Check what data has changed (unless force sync)
            sync_products = force
            sync_orders = force
            sync_customers = force

            if not force:
                logger.info("Checking for individual data changes", extra={'store_id': store_id, 'user_id': user_id})

                # Check each data type individually
                # If hash is None (newly created assistant), consider it as changed
                if assistant_record.products_hash is None or assistant_record.products_hash != products_hash:
                    sync_products = True
                    if assistant_record.products_hash is None:
                        logger.info("Products hash is None (new assistant), will sync", extra={'store_id': store_id, 'user_id': user_id})
                    else:
                        logger.info("Products data changed, will sync", extra={'store_id': store_id, 'user_id': user_id})

                if assistant_record.orders_hash is None or assistant_record.orders_hash != orders_hash:
                    sync_orders = True
                    if assistant_record.orders_hash is None:
                        logger.info("Orders hash is None (new assistant), will sync", extra={'store_id': store_id, 'user_id': user_id})
                    else:
                        logger.info("Orders data changed, will sync", extra={'store_id': store_id, 'user_id': user_id})

                if assistant_record.customers_hash is None or assistant_record.customers_hash != customers_hash:
                    sync_customers = True
                    if assistant_record.customers_hash is None:
                        logger.info("Customers hash is None (new assistant), will sync", extra={'store_id': store_id, 'user_id': user_id})
                    else:
                        logger.info("Customers data changed, will sync", extra={'store_id': store_id, 'user_id': user_id})

                # If no changes detected, skip sync
                if not (sync_products or sync_orders or sync_customers):
                    logger.info("No changes detected in any data type, skipping sync", extra={'store_id': store_id, 'user_id': user_id})
                    # No changes - just update timestamp
                    AssistantCRUD.update_assistant(
                        db, assistant_record.id,
                        status='active',
                        last_sync=datetime.now(timezone.utc)
                    )

                    sync_duration = (datetime.now(timezone.utc) - sync_start).total_seconds()
                    return DataSyncResult(
                        success=True,
                        message="No changes detected, sync skipped",
                        sync_duration=sync_duration
                    )

            # Log what will be synced
            sync_types = []
            if sync_products: sync_types.append("products")
            if sync_orders: sync_types.append("orders")
            if sync_customers: sync_types.append("customers")

            logger.info(f"Performing selective sync for: {', '.join(sync_types)}", extra={
                'store_id': store_id, 'user_id': user_id, 'force': force,
                'extra_data': {
                    'sync_products': sync_products,
                    'sync_orders': sync_orders,
                    'sync_customers': sync_customers
                }
            })

            # Perform selective data sync
            result = assistant.selective_sync(
                products_data=products_data if sync_products else None,
                orders_data=orders_data if sync_orders else None,
                customers_data=customers_data if sync_customers else None,
                sync_products=sync_products,
                sync_orders=sync_orders,
                sync_customers=sync_customers
            )
            sync_duration = (datetime.now(timezone.utc) - sync_start).total_seconds()

            if result and result.get("success"):
                # Update sync status with new hashes (only for successfully synced data)
                update_data = {}
                if sync_products and "products" in result.get("synced_files", []):
                    update_data["products_hash"] = products_hash
                if sync_orders and "orders" in result.get("synced_files", []):
                    update_data["orders_hash"] = orders_hash
                if sync_customers and "customers" in result.get("synced_files", []):
                    update_data["customers_hash"] = customers_hash

                if update_data:
                    AssistantCRUD.update_sync_status(db, assistant_record.id, **update_data)

                # Also update status to active
                AssistantCRUD.update_assistant(db, assistant_record.id, status='active')

                # Create detailed success message
                synced_files = result.get("synced_files", [])
                if synced_files:
                    message = f"Selective sync completed successfully. Updated: {', '.join(synced_files)}"
                else:
                    message = "Sync completed (no files needed updating)"

                logger.info("Sync completed successfully", extra={
                    'store_id': store_id, 'user_id': user_id,
                    'extra_data': {
                        'synced_files': synced_files,
                        'duration': sync_duration
                    }
                })

                return DataSyncResult(
                    success=True,
                    message=message,
                    sync_duration=sync_duration
                )
            else:
                # Handle partial or complete failure
                errors = result.get("errors", ["Unknown sync error"]) if result else ["Sync operation failed"]
                synced_files = result.get("synced_files", []) if result else []

                # Update status based on whether any files were synced
                if synced_files:
                    # Partial success - update what we can and set status to active
                    update_data = {}
                    if sync_products and "products" in synced_files:
                        update_data["products_hash"] = products_hash
                    if sync_orders and "orders" in synced_files:
                        update_data["orders_hash"] = orders_hash
                    if sync_customers and "customers" in synced_files:
                        update_data["customers_hash"] = customers_hash

                    if update_data:
                        AssistantCRUD.update_sync_status(db, assistant_record.id, **update_data)

                    AssistantCRUD.update_assistant(db, assistant_record.id, status='active')

                    message = f"Partial sync completed. Updated: {', '.join(synced_files)}, Errors: {len(errors)}"
                    success = True
                else:
                    # Complete failure
                    AssistantCRUD.update_assistant(db, assistant_record.id, status='error')
                    message = "Data sync failed completely"
                    success = False

                logger.error("Sync completed with errors", extra={
                    'store_id': store_id, 'user_id': user_id,
                    'extra_data': {
                        'synced_files': synced_files,
                        'errors': errors,
                        'duration': sync_duration
                    }
                })

                return DataSyncResult(
                    success=success,
                    message=message,
                    errors=errors,
                    sync_duration=sync_duration
                )

        except Exception as e:
            # Update status to error
            AssistantCRUD.update_assistant(db, assistant_record.id, status='error')

            return DataSyncResult(
                success=False,
                message="Data sync failed",
                errors=[str(e)]
            )

    def _calculate_data_hash(self, data: dict) -> str:
        """Calculate MD5 hash of data for change detection"""
        import hashlib
        import json

        def sort_nested_dict(obj):
            """Recursively sort nested dictionaries and lists"""
            if isinstance(obj, dict):
                return {k: sort_nested_dict(v) for k, v in sorted(obj.items())}
            elif isinstance(obj, list):
                # Sort list items recursively first
                sorted_items = [sort_nested_dict(item) for item in obj]
                # If items are dictionaries with 'id' field, sort by id for consistency
                if sorted_items and isinstance(sorted_items[0], dict) and 'id' in sorted_items[0]:
                    return sorted(sorted_items, key=lambda x: x.get('id', 0))
                else:
                    # For other lists, convert to string and sort for consistency
                    return sorted(sorted_items, key=lambda x: json.dumps(x, sort_keys=True, default=str))
            else:
                return obj

        # Sort the data structure recursively
        sorted_data = sort_nested_dict(data)

        # Convert to a consistent string representation
        data_str = json.dumps(sorted_data, sort_keys=True, default=str)
        return hashlib.md5(data_str.encode()).hexdigest()

    def get_assistant_stats(self, db: Session, assistant_id: int) -> AssistantStats:
        """Get statistics for an assistant"""
        assistant = AssistantCRUD.get_assistant_by_store(db, assistant_id)
        if not assistant:
            return AssistantStats()

        # For now, return basic stats
        # In a full implementation, you might track conversations in a separate table
        return AssistantStats(
            total_conversations=0,  # Would be calculated from conversation history
            total_messages=0,       # Would be calculated from message history
            last_conversation=None, # Would be from conversation history
            data_files_count=sum([
                1 for file_id in [
                    assistant.products_file_id,
                    assistant.orders_file_id,
                    assistant.customers_file_id
                ] if file_id
            ]),
            last_data_sync=assistant.last_sync
        )

    def delete_assistant(self, db: Session, store_id: int, user_id: int) -> bool:
        """
        Delete assistant for a store with shared assistant logic.

        - Always deletes the user's assistant database record
        - Only deletes Pinecone assistant if no other users have the same store
        """
        # Verify store ownership
        store = StoreCRUD.get_store_by_id(db, store_id)
        if not store or store.user_id != user_id:
            return False

        assistant = AssistantCRUD.get_assistant_by_store(db, store_id)
        if not assistant:
            return True  # Already deleted

        try:
            # Check if other users have the same Shopify store
            has_other_users = StoreCRUD.check_if_store_has_other_users(
                db, store.shopify_store_id, exclude_user_id=user_id
            )

            # Remove from cache
            if store_id in self.active_assistants:
                del self.active_assistants[store_id]
            if store_id in self.assistant_cache_timestamps:
                del self.assistant_cache_timestamps[store_id]

            if has_other_users:
                # Other users have this store - only delete the database record
                print(f"Store {store.shopify_store_id} has other users - keeping Pinecone assistant, deleting DB record only")
                db.delete(assistant)
                db.commit()
            else:
                # No other users have this store - delete both DB record and Pinecone assistant
                print(f"Store {store.shopify_store_id} has no other users - deleting Pinecone assistant completely")
                try:
                    # Delete the Pinecone assistant instance
                    from pinecone_assistant import PineconeAssistant
                    pinecone_assistant = PineconeAssistant(assistant_name=assistant.assistant_name)
                    pinecone_assistant.delete_assistant()
                    print(f"Successfully deleted Pinecone assistant: {assistant.assistant_name}")
                except Exception as e:
                    print(f"Warning: Failed to delete Pinecone assistant {assistant.assistant_name}: {e}")

                # Delete the database record
                db.delete(assistant)
                db.commit()

            return True

        except Exception as e:
            print(f"Error deleting assistant: {e}")
            db.rollback()
            return False

    def get_user_assistants(self, db: Session, user_id: int) -> List[Assistant]:
        """Get all assistants for a user"""
        # Get user's stores
        stores = StoreCRUD.get_stores_by_user(db, user_id)

        assistants = []
        for store in stores:
            assistant = AssistantCRUD.get_assistant_by_store(db, store.id)
            if assistant:
                # Add store information to assistant
                assistant.store_name = store.store_name
                assistant.shop_url = store.shop_url
                assistant.store_status = store.status
                assistants.append(assistant)

        return assistants

    def clear_cache(self):
        """Clear assistant cache"""
        self.active_assistants.clear()
        self.assistant_cache_timestamps.clear()

    def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information for debugging"""
        return {
            "cached_assistants": len(self.active_assistants),
            "cache_timestamps": {
                store_id: timestamp.isoformat()
                for store_id, timestamp in self.assistant_cache_timestamps.items()
            },
            "cache_ttl_seconds": self.assistant_cache_ttl
        }

# Global instance
multi_store_manager = MultiStoreAssistantManager()
