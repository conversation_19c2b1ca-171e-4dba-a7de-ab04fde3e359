from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Optional
import base64
from datetime import datetime, timezone

from .models import User, Store, Assistant

class UserCRUD:
    """CRUD operations for User model"""

    @staticmethod
    def create_user(db: Session, username: str, email: str, password_hash: str) -> User:
        """Create a new user"""
        user = User(
            username=username,
            email=email,
            password_hash=password_hash
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    @staticmethod
    def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
        """Get user by ID"""
        return db.query(User).filter(User.id == user_id).first()

    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        """Get user by username"""
        return db.query(User).filter(User.username == username).first()

    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[User]:
        """Get user by email"""
        return db.query(User).filter(User.email == email).first()

    @staticmethod
    def update_user(db: Session, user_id: int, **kwargs) -> Optional[User]:
        """Update user information"""
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            for key, value in kwargs.items():
                if hasattr(user, key):
                    setattr(user, key, value)
            db.commit()
            db.refresh(user)
        return user

class StoreCRUD:
    """CRUD operations for Store model"""

    @staticmethod
    def encode_token(token: str) -> str:
        """Encode access token for secure storage"""
        return base64.urlsafe_b64encode(token.encode()).decode()

    @staticmethod
    def decode_token(encoded_token: str) -> str:
        """Decode access token for use"""
        return base64.urlsafe_b64decode(encoded_token.encode()).decode()

    @staticmethod
    def create_store(db: Session, user_id: int, shopify_store_id: str,
                    shop_url: str, access_token: str, **kwargs) -> Store:
        """Create a new store"""
        store = Store(
            user_id=user_id,
            shopify_store_id=shopify_store_id,
            shop_url=shop_url,
            access_token_encrypted=StoreCRUD.encode_token(access_token),
            **kwargs
        )
        db.add(store)
        db.commit()
        db.refresh(store)
        return store

    @staticmethod
    def get_store_by_id(db: Session, store_id: int) -> Optional[Store]:
        """Get store by ID"""
        return db.query(Store).filter(Store.id == store_id).first()

    @staticmethod
    def get_stores_by_user(db: Session, user_id: int, active_only: bool = True) -> List[Store]:
        """Get all stores for a user"""
        query = db.query(Store).filter(Store.user_id == user_id)
        if active_only:
            query = query.filter(and_(Store.is_deleted == False, Store.status == "active"))
        return query.all()

    @staticmethod
    def get_store_by_shopify_id(db: Session, shopify_store_id: str, include_deleted: bool = False) -> Optional[Store]:
        """
        Get store by Shopify store ID

        Args:
            db: Database session
            shopify_store_id: The Shopify store ID to search for
            include_deleted: Whether to include soft-deleted stores (default: False)

        Returns:
            Store object if found, None otherwise
        """
        query = db.query(Store).filter(Store.shopify_store_id == shopify_store_id)

        # By default, exclude soft-deleted stores
        if not include_deleted:
            query = query.filter(Store.is_deleted == False)

        return query.first()

    @staticmethod
    def get_store_by_user_and_shopify_id(db: Session, user_id: int, shopify_store_id: str, include_deleted: bool = False) -> Optional[Store]:
        """
        Get store by user ID and Shopify store ID

        Args:
            db: Database session
            user_id: The user ID to search for
            shopify_store_id: The Shopify store ID to search for
            include_deleted: Whether to include soft-deleted stores (default: False)

        Returns:
            Store object if found, None otherwise
        """
        query = db.query(Store).filter(
            Store.user_id == user_id,
            Store.shopify_store_id == shopify_store_id
        )

        # By default, exclude soft-deleted stores
        if not include_deleted:
            query = query.filter(Store.is_deleted == False)

        return query.first()

    @staticmethod
    def get_store_by_user_and_domain(db: Session, user_id: int, myshopify_domain: str, include_deleted: bool = False) -> Optional[Store]:
        """
        Get store by user ID and myshopify domain

        Args:
            db: Database session
            user_id: The user ID to search for
            myshopify_domain: The myshopify domain to search for
            include_deleted: Whether to include soft-deleted stores (default: False)

        Returns:
            Store object if found, None otherwise
        """
        query = db.query(Store).filter(
            Store.user_id == user_id,
            Store.myshopify_domain == myshopify_domain
        )

        # By default, exclude soft-deleted stores
        if not include_deleted:
            query = query.filter(Store.is_deleted == False)

        return query.first()

    @staticmethod
    def update_store(db: Session, store_id: int, **kwargs) -> Optional[Store]:
        """Update store information"""
        store = db.query(Store).filter(Store.id == store_id).first()
        if store:
            # Handle access token encoding if provided
            if 'access_token' in kwargs:
                kwargs['access_token_encrypted'] = StoreCRUD.encode_token(kwargs.pop('access_token'))

            for key, value in kwargs.items():
                if hasattr(store, key):
                    setattr(store, key, value)
            db.commit()
            db.refresh(store)
        return store

    @staticmethod
    def delete_store(db: Session, store_id: int, soft_delete: bool = True) -> bool:
        """Delete store (soft delete by default)"""
        store = db.query(Store).filter(Store.id == store_id).first()
        if store:
            if soft_delete:
                store.is_deleted = True
                store.status = "inactive"
                db.commit()
            else:
                db.delete(store)
                db.commit()
            return True
        return False

    @staticmethod
    def check_if_store_has_other_users(db: Session, shopify_store_id: str, exclude_user_id: int = None) -> bool:
        """
        Check if other users have the same Shopify store connected.

        Args:
            db: Database session
            shopify_store_id: The Shopify store ID to check
            exclude_user_id: User ID to exclude from the check (optional)

        Returns:
            True if other users have this store, False otherwise
        """
        query = db.query(Store).filter(
            Store.shopify_store_id == shopify_store_id,
            Store.is_deleted == False,
            Store.status == "active"
        )

        # Exclude specific user if provided
        if exclude_user_id:
            query = query.filter(Store.user_id != exclude_user_id)

        # Check if any other active stores exist for this Shopify store
        other_stores_count = query.count()
        return other_stores_count > 0

    @staticmethod
    def get_store_with_shopify_details(db: Session, store_id: int) -> Optional[tuple]:
        """
        Get store with its Shopify store ID and user ID.

        Returns:
            Tuple of (store, shopify_store_id, user_id) or None
        """
        store = db.query(Store).filter(Store.id == store_id).first()
        if store:
            return (store, store.shopify_store_id, store.user_id)
        return None

class AssistantCRUD:
    """CRUD operations for Assistant model"""

    @staticmethod
    def create_assistant(db: Session, store_id: int, assistant_name: str,
                        pinecone_assistant_id: str = None) -> Assistant:
        """Create a new assistant"""
        assistant = Assistant(
            store_id=store_id,
            assistant_name=assistant_name,
            pinecone_assistant_id=pinecone_assistant_id
        )
        db.add(assistant)
        db.commit()
        db.refresh(assistant)
        return assistant

    @staticmethod
    def get_assistant_by_store(db: Session, store_id: int) -> Optional[Assistant]:
        """Get assistant for a store"""
        return db.query(Assistant).filter(Assistant.store_id == store_id).first()

    @staticmethod
    def get_assistant_by_name(db: Session, assistant_name: str) -> Optional[Assistant]:
        """Get assistant by name"""
        return db.query(Assistant).filter(Assistant.assistant_name == assistant_name).first()

    @staticmethod
    def update_assistant(db: Session, assistant_id: int, **kwargs) -> Optional[Assistant]:
        """Update assistant information"""
        assistant = db.query(Assistant).filter(Assistant.id == assistant_id).first()
        if assistant:
            for key, value in kwargs.items():
                if hasattr(assistant, key):
                    setattr(assistant, key, value)
            db.commit()
            db.refresh(assistant)
        return assistant

    @staticmethod
    def update_sync_status(db: Session, assistant_id: int,
                          products_hash: str = None, orders_hash: str = None,
                          customers_hash: str = None) -> Optional[Assistant]:
        """Update assistant sync status and data hashes"""
        assistant = db.query(Assistant).filter(Assistant.id == assistant_id).first()
        if assistant:
            assistant.last_sync = datetime.now(timezone.utc)
            if products_hash:
                assistant.products_hash = products_hash
            if orders_hash:
                assistant.orders_hash = orders_hash
            if customers_hash:
                assistant.customers_hash = customers_hash
            db.commit()
            db.refresh(assistant)
        return assistant
