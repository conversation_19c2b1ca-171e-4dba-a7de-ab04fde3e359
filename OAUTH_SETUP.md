# Shopify OAuth Configuration Guide

## Current Configuration Status ✅

The OAuth configuration has been successfully set up for the Pinecone E-commerce Assistant project.

### Environment Variables Configured

```env
# Shopify OAuth Configuration
SHOPIFY_API_KEY=f9a4803317f2115e8e664a1ea2a64458
SHOPIFY_API_SECRET=2f7c4fc86d6dab021e00a2c778389529
SHOPIFY_API_VERSION=2025-01
SHOPIFY_SCOPES=read_products,read_customers,read_orders
SHOPIFY_REDIRECT_URI=https://2443-106-201-144-47.ngrok-free.app/stores/oauth/callback
SHOPIFY_GRAPHQL_URI=https://{shop}/admin/api/{version}/graphql.json

# Development Environment
NGROK_BASE_URL=https://2443-106-201-144-47.ngrok-free.app
```

## OAuth Flow Endpoints

### Installation Endpoint
```
GET https://2443-106-201-144-47.ngrok-free.app/stores/oauth/install?shop=SHOP_NAME.myshopify.com
```

### Callback Endpoint
```
GET https://2443-106-201-144-47.ngrok-free.app/stores/oauth/callback
```

## Shopify App Configuration Requirements

When setting up your Shopify app in the Partner Dashboard, use these settings:

### App URLs
- **App URL**: `https://2443-106-201-144-47.ngrok-free.app`
- **Allowed redirection URL(s)**: `https://2443-106-201-144-47.ngrok-free.app/stores/oauth/callback`

### App Scopes
- `read_products` - Access to product data
- `read_customers` - Access to customer data  
- `read_orders` - Access to order data

## Configuration Management

### Settings Class
The project now includes a centralized configuration management system in `config.py`:

```python
from config import settings, OAuthConfig

# Access OAuth settings
api_key = settings.shopify_api_key
api_secret = settings.shopify_api_secret

# Generate OAuth URLs
install_url = settings.get_oauth_install_url("mystore.myshopify.com")
graphql_url = settings.get_shopify_graphql_url("mystore.myshopify.com")

# OAuth helpers
token_url = OAuthConfig.get_token_exchange_url("mystore.myshopify.com")
headers = OAuthConfig.get_graphql_headers("access_token")
```

### Configuration Validation
Test your configuration anytime by running:

```bash
python test_oauth_config.py
```

## Development Workflow

### 1. Start ngrok
Make sure your ngrok tunnel is running and accessible:
```bash
ngrok http 8000
```

### 2. Update ngrok URL (if changed)
If your ngrok URL changes, update these environment variables:
- `NGROK_BASE_URL`
- `SHOPIFY_REDIRECT_URI`

### 3. Test Configuration
Run the configuration test to ensure everything is working:
```bash
python test_oauth_config.py
```

## Next Implementation Steps

### Phase 2: OAuth Service Implementation
- Create OAuth service functions (from plan.md)
- Implement token exchange logic
- Add HMAC verification for security

### Phase 3: API Routes
- Add `/stores/oauth/install` endpoint
- Add `/stores/oauth/callback` endpoint
- Remove existing `/stores/manual` endpoint

### Phase 4: Frontend Integration
- Replace manual token forms with OAuth button
- Update store connection UI

## Security Notes

- ✅ HMAC verification will be implemented for callback security
- ✅ Access tokens are Base64 encoded before database storage
- ✅ OAuth flow follows Shopify's recommended practices
- ✅ Ngrok URLs are used for development (not localhost)

## Troubleshooting

### Common Issues
1. **Invalid redirect URI**: Ensure the redirect URI in Shopify app settings matches `SHOPIFY_REDIRECT_URI`
2. **ngrok tunnel expired**: Update `NGROK_BASE_URL` and `SHOPIFY_REDIRECT_URI` with new tunnel URL
3. **Scope mismatch**: Verify app scopes match `SHOPIFY_SCOPES` setting

### Testing OAuth Configuration
Always run the test script after making changes:
```bash
python test_oauth_config.py
```

## Configuration Files Modified

1. **`.env`** - Added OAuth environment variables
2. **`config.py`** - New centralized configuration management
3. **`test_oauth_config.py`** - Configuration validation script
4. **`README.md`** - Updated documentation
5. **`OAUTH_SETUP.md`** - This reference guide

---

**Status**: ✅ Phase 1 Complete - OAuth configuration is ready for implementation!
