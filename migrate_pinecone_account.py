#!/usr/bin/env python3
"""
Pinecone Account Migration Script

This script handles the database migration when switching to a new Pinecone account.
It updates the database schema and cleans up invalid Pinecone resource references.
"""

import os
import sys
from dotenv import load_dotenv
from sqlalchemy import text

# Load environment variables
load_dotenv()

def migrate_database():
    """Migrate database for new Pinecone account"""
    try:
        from database.session import SessionLocal, engine
        from database.models import Base
        
        print("🚀 Starting Pinecone Account Migration")
        print("=" * 50)
        
        # Create database session
        db = SessionLocal()
        
        try:
            # Step 1: Update database schema to allow NULL pinecone_assistant_id
            print("\n📋 Step 1: Updating database schema...")
            
            # Check if the column already allows NULL
            result = db.execute(text("""
                SELECT IS_NULLABLE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'assistants' 
                AND COLUMN_NAME = 'pinecone_assistant_id'
            """)).fetchone()
            
            if result and result[0] == 'NO':
                print("Updating pinecone_assistant_id column to allow NULL values...")
                db.execute(text("""
                    ALTER TABLE assistants 
                    MODIFY COLUMN pinecone_assistant_id VARCHAR(255) NULL
                """))
                db.commit()
                print("✅ Database schema updated successfully")
            else:
                print("✅ Database schema already supports NULL values")
            
            # Step 2: Clear invalid Pinecone resource references
            print("\n📋 Step 2: Clearing invalid Pinecone resource references...")
            
            # Reset all Pinecone-specific data
            result = db.execute(text("""
                UPDATE assistants SET 
                    pinecone_assistant_id = NULL,
                    products_file_id = NULL,
                    orders_file_id = NULL,
                    customers_file_id = NULL,
                    products_hash = NULL,
                    orders_hash = NULL,
                    customers_hash = NULL,
                    status = 'inactive',
                    last_sync = NULL
            """))
            
            affected_rows = result.rowcount
            db.commit()
            
            print(f"✅ Cleared Pinecone references from {affected_rows} assistant records")
            
            # Step 3: Verify migration
            print("\n📋 Step 3: Verifying migration...")
            
            # Count assistants that need recreation
            result = db.execute(text("""
                SELECT COUNT(*) as count 
                FROM assistants 
                WHERE pinecone_assistant_id IS NULL
            """)).fetchone()
            
            assistants_to_recreate = result[0] if result else 0
            
            print(f"✅ Migration completed successfully!")
            print(f"   - {assistants_to_recreate} assistants will be recreated with new Pinecone account")
            
            return True
            
        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            db.rollback()
            return False
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def verify_pinecone_connection():
    """Verify connection to new Pinecone account"""
    try:
        from pinecone import Pinecone
        
        print("\n📋 Verifying Pinecone connection...")
        
        pinecone_api_key = os.getenv("PINECONE_API_KEY")
        if not pinecone_api_key:
            print("❌ PINECONE_API_KEY not found in environment variables")
            return False
        
        # Test connection
        pc = Pinecone(api_key=pinecone_api_key)
        assistants = pc.assistant.list_assistants()
        
        print(f"✅ Connected to Pinecone successfully!")
        print(f"   - Found {len(assistants)} existing assistants in new account")
        
        return True
        
    except Exception as e:
        print(f"❌ Pinecone connection failed: {str(e)}")
        print("   Please verify your PINECONE_API_KEY is correct")
        return False

def main():
    """Main migration function"""
    print("🔄 Pinecone Account Migration Tool")
    print("This script will migrate your database to work with a new Pinecone account")
    print()
    
    # Verify Pinecone connection first
    if not verify_pinecone_connection():
        print("\n❌ Migration aborted due to Pinecone connection issues")
        sys.exit(1)
    
    # Perform database migration
    if not migrate_database():
        print("\n❌ Migration failed")
        sys.exit(1)
    
    print("\n🎉 Migration completed successfully!")
    print("\nNext steps:")
    print("1. Restart your application: python main.py")
    print("2. Connect your Shopify stores - assistants will be automatically recreated")
    print("3. All store data will be re-synced to the new Pinecone account")
    print("4. Monitor the application logs for any issues")

if __name__ == "__main__":
    main()
