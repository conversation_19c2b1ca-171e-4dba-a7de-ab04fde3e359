#!/usr/bin/env python3
"""
Test Stores API Endpoint

This script tests the stores API endpoint directly to verify it's working correctly.
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_stores_api_direct():
    """Test stores API endpoint directly using database session"""
    try:
        from database.session import SessionLocal
        from database.crud import UserCRUD
        from stores.service import StoreService
        
        print("🧪 Testing Stores API Logic Directly")
        print("=" * 50)
        
        db = SessionLocal()
        store_service = StoreService()
        
        try:
            # Get user dev (ID: 1)
            user = UserCRUD.get_user_by_id(db, 1)
            if not user:
                print("❌ User 'dev' not found")
                return False
            
            print(f"✅ Found user: {user.username} (ID: {user.id})")
            
            # Test get_user_stores without stats
            print("\n📋 Testing get_user_stores (without stats):")
            stores_no_stats = store_service.get_user_stores(db, user.id, include_stats=False)
            print(f"  Found {len(stores_no_stats)} stores")
            for store in stores_no_stats:
                print(f"    - {store.store_name} (ID: {store.id}, Status: {store.status})")
            
            # Test get_user_stores with stats
            print("\n📋 Testing get_user_stores (with stats):")
            stores_with_stats = store_service.get_user_stores(db, user.id, include_stats=True)
            print(f"  Found {len(stores_with_stats)} stores")
            for store in stores_with_stats:
                print(f"    - {store.store_name} (ID: {store.id}, Status: {store.status})")
                if hasattr(store, 'stats') and store.stats:
                    print(f"      Stats: Products: {store.stats.total_products}, Orders: {store.stats.total_orders}")
                if hasattr(store, 'assistant_status'):
                    print(f"      Assistant: {store.assistant_status}")
            
            return True
            
        except Exception as e:
            print(f"❌ Direct API test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Direct API test setup failed: {str(e)}")
        return False

def test_pydantic_serialization():
    """Test Pydantic model serialization"""
    try:
        from database.session import SessionLocal
        from database.crud import UserCRUD
        from stores.service import StoreService
        from stores.models import StoreWithStats
        
        print("\n🔄 Testing Pydantic Serialization")
        print("=" * 50)
        
        db = SessionLocal()
        store_service = StoreService()
        
        try:
            # Get stores
            stores = store_service.get_user_stores(db, 1, include_stats=True)
            
            print(f"Found {len(stores)} stores to serialize")
            
            # Test serialization
            serialized_stores = []
            for store in stores:
                try:
                    serialized = StoreWithStats.model_validate(store)
                    serialized_stores.append(serialized)
                    print(f"✅ Successfully serialized store: {store.store_name}")
                    
                    # Convert to dict to see the output
                    store_dict = serialized.model_dump()
                    print(f"   Serialized data: {json.dumps(store_dict, indent=2, default=str)}")
                    
                except Exception as e:
                    print(f"❌ Failed to serialize store {store.store_name}: {str(e)}")
                    import traceback
                    traceback.print_exc()
            
            print(f"\n✅ Successfully serialized {len(serialized_stores)} stores")
            return True
            
        except Exception as e:
            print(f"❌ Serialization test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Serialization test setup failed: {str(e)}")
        return False

def test_http_endpoint():
    """Test the actual HTTP endpoint"""
    try:
        print("\n🌐 Testing HTTP Endpoint")
        print("=" * 50)
        
        # Test without authentication first
        print("📋 Testing endpoint without authentication:")
        try:
            response = requests.get("http://localhost:8000/stores/?include_stats=true")
            print(f"  Status Code: {response.status_code}")
            print(f"  Response: {response.text[:200]}...")
        except Exception as e:
            print(f"  ❌ Request failed: {str(e)}")
        
        print("\n⚠️  To test with authentication:")
        print("1. Open your browser and login to the application")
        print("2. Open Developer Tools (F12)")
        print("3. Go to Application/Storage tab")
        print("4. Find the JWT token in localStorage or sessionStorage")
        print("5. Run this command:")
        print("   curl -H 'Authorization: Bearer YOUR_JWT_TOKEN' http://localhost:8000/stores/?include_stats=true")
        
        return True
        
    except Exception as e:
        print(f"❌ HTTP endpoint test failed: {str(e)}")
        return False

def check_frontend_files():
    """Check frontend files for potential issues"""
    try:
        print("\n📁 Checking Frontend Files")
        print("=" * 50)
        
        # Check if stores.js exists and has the right functions
        stores_js_path = "static/js/stores.js"
        if os.path.exists(stores_js_path):
            print(f"✅ Found {stores_js_path}")
            
            with open(stores_js_path, 'r') as f:
                content = f.read()
                
            # Check for key functions
            functions_to_check = [
                'loadStores',
                'renderStores',
                'checkOAuthCallback',
                'getAuthHeaders'
            ]
            
            for func in functions_to_check:
                if func in content:
                    print(f"  ✅ Found function: {func}")
                else:
                    print(f"  ❌ Missing function: {func}")
            
            # Check for API endpoint
            if '/stores/?include_stats=true' in content:
                print("  ✅ Found correct API endpoint")
            else:
                print("  ❌ API endpoint not found or incorrect")
                
        else:
            print(f"❌ {stores_js_path} not found")
        
        # Check if dashboard.html exists
        dashboard_path = "templates/dashboard.html"
        if os.path.exists(dashboard_path):
            print(f"✅ Found {dashboard_path}")
        else:
            print(f"❌ {dashboard_path} not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Frontend files check failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Stores API Test Suite")
    print("This tool tests the stores API endpoint and related functionality")
    print()
    
    success = True
    
    # Test API logic directly
    if not test_stores_api_direct():
        success = False
    
    # Test Pydantic serialization
    if not test_pydantic_serialization():
        success = False
    
    # Test HTTP endpoint
    if not test_http_endpoint():
        success = False
    
    # Check frontend files
    if not check_frontend_files():
        success = False
    
    if success:
        print("\n✅ All tests completed!")
        print("\nNext steps to debug frontend:")
        print("1. Check browser console for JavaScript errors")
        print("2. Check Network tab in dev tools for failed API requests")
        print("3. Verify JWT token is being sent correctly")
        print("4. Try hard refresh (Ctrl+F5) to clear cache")
    else:
        print("\n❌ Some tests failed - check the output above")

if __name__ == "__main__":
    main()
